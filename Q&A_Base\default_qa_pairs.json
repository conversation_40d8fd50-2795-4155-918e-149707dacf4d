{"metadata": {"version": "1.0", "created_at": **********, "total_pairs": 15, "description": "默认问答对数据集，用于系统初始化"}, "qa_pairs": [{"id": "qa_001", "question": "什么是GuiXiaoXiRag？", "answer": "GuiXiaoXiRag是一个基于FastAPI的智能知识问答系统，集成了知识图谱、向量检索、意图识别等多种AI技术，提供强大的知识管理和智能查询功能。主要特性包括多模式查询、文档管理、知识库管理、知识图谱构建等。", "category": "system", "confidence": 1.0, "keywords": ["GuiXiaoXiRag", "系统介绍", "知识问答", "FastAPI"], "source": "system_default", "created_at": **********, "updated_at": **********}, {"id": "qa_002", "question": "如何使用问答系统？", "answer": "使用问答系统很简单：1) 通过API接口提交问题；2) 系统使用向量相似度匹配找到最相关的答案；3) 支持单个查询和批量查询；4) 可以添加新的问答对来扩展知识库；5) 支持分类管理和统计分析。", "category": "usage", "confidence": 1.0, "keywords": ["使用方法", "API", "查询", "向量匹配"], "source": "user_guide", "created_at": **********, "updated_at": **********}, {"id": "qa_003", "question": "问答系统支持哪些功能？", "answer": "问答系统支持以下功能：1) 智能问答查询；2) 问答对的增删改查；3) 批量导入导出；4) 相似度搜索；5) 分类管理；6) 统计分析；7) 数据备份恢复；8) 健康检查；9) 配置管理等。", "category": "features", "confidence": 1.0, "keywords": ["功能", "特性", "能力", "管理"], "source": "feature_list", "created_at": **********, "updated_at": **********}, {"id": "qa_004", "question": "如何提高问答匹配的准确性？", "answer": "提高匹配准确性的方法：1) 添加更多高质量的问答对；2) 优化问题的表述，使用清晰准确的语言；3) 合理设置相似度阈值；4) 为问答对添加相关关键词；5) 定期更新和维护问答库；6) 使用合适的分类标签。", "category": "optimization", "confidence": 0.9, "keywords": ["准确性", "优化", "匹配", "质量"], "source": "best_practices", "created_at": **********, "updated_at": **********}, {"id": "qa_005", "question": "问答系统的API端点有哪些？", "answer": "主要API端点包括：/api/v1/qa/health（健康检查）、/api/v1/qa/pairs（问答对管理）、/api/v1/qa/query（单个查询）、/api/v1/qa/query/batch（批量查询）、/api/v1/qa/statistics（统计信息）、/api/v1/qa/import（导入）、/api/v1/qa/export（导出）等。", "category": "api", "confidence": 1.0, "keywords": ["API", "端点", "接口", "路由"], "source": "api_documentation", "created_at": **********, "updated_at": **********}, {"id": "qa_006", "question": "如何批量导入问答对？", "answer": "批量导入问答对的步骤：1) 准备JSON格式的数据文件；2) 使用/api/v1/qa/import端点上传文件；3) 设置导入参数（如是否覆盖、默认分类等）；4) 系统会自动处理并返回导入结果。支持JSON、CSV、Excel等格式。", "category": "import", "confidence": 0.95, "keywords": ["批量导入", "JSON", "CSV", "Excel", "数据"], "source": "import_guide", "created_at": **********, "updated_at": **********}, {"id": "qa_007", "question": "问答系统如何进行向量化？", "answer": "问答系统使用embedding模型对问题进行向量化：1) 调用配置的embedding服务；2) 将问题文本转换为高维向量；3) 存储向量用于相似度计算；4) 查询时计算余弦相似度；5) 返回最相似的问答对。支持多种embedding模型。", "category": "technical", "confidence": 0.9, "keywords": ["向量化", "embedding", "相似度", "余弦相似度"], "source": "technical_docs", "created_at": **********, "updated_at": **********}, {"id": "qa_008", "question": "如何配置问答系统的参数？", "answer": "配置问答系统参数的方法：1) 修改.env文件中的相关配置；2) 设置相似度阈值（qa_similarity_threshold）；3) 配置最大返回结果数（qa_max_results）；4) 设置embedding服务地址；5) 配置存储路径等。重启服务后生效。", "category": "configuration", "confidence": 0.95, "keywords": ["配置", "参数", "阈值", "设置"], "source": "configuration_guide", "created_at": **********, "updated_at": **********}, {"id": "qa_009", "question": "问答系统支持哪些文件格式？", "answer": "问答系统支持多种文件格式：导入支持JSON、CSV、Excel（.xlsx）格式；导出支持JSON、CSV、Excel格式；备份文件为JSON格式。JSON格式最为完整，包含所有字段信息。", "category": "formats", "confidence": 1.0, "keywords": ["文件格式", "JSON", "CSV", "Excel", "导入导出"], "source": "format_specification", "created_at": **********, "updated_at": **********}, {"id": "qa_010", "question": "如何监控问答系统的性能？", "answer": "监控问答系统性能的方法：1) 使用/api/v1/qa/health端点检查健康状态；2) 通过/api/v1/qa/statistics获取详细统计信息；3) 监控响应时间、成功率、错误率等指标；4) 查看问答对数量、分类分布等数据；5) 定期检查系统日志。", "category": "monitoring", "confidence": 0.9, "keywords": ["监控", "性能", "统计", "健康检查"], "source": "monitoring_guide", "created_at": **********, "updated_at": **********}, {"id": "qa_011", "question": "问答系统如何处理中文查询？", "answer": "问答系统对中文查询的处理：1) 使用支持中文的embedding模型；2) 自动处理中文分词和语义理解；3) 支持中文关键词匹配；4) 优化中文相似度计算；5) 支持中文分类和标签。系统已针对中文进行优化。", "category": "chinese", "confidence": 0.95, "keywords": ["中文", "分词", "语义理解", "中文优化"], "source": "chinese_support", "created_at": **********, "updated_at": **********}, {"id": "qa_012", "question": "如何备份和恢复问答数据？", "answer": "备份和恢复问答数据的方法：1) 使用/api/v1/qa/backup端点创建备份；2) 备份文件包含所有问答对和向量数据；3) 支持压缩备份以节省空间；4) 使用/api/v1/qa/restore端点恢复数据；5) 可选择是否覆盖现有数据。建议定期备份。", "category": "backup", "confidence": 1.0, "keywords": ["备份", "恢复", "数据保护", "压缩"], "source": "backup_guide", "created_at": **********, "updated_at": **********}, {"id": "qa_013", "question": "问答系统的相似度阈值如何设置？", "answer": "相似度阈值设置建议：1) 默认值0.98适合高精度匹配；2) 提高阈值（0.99+）获得更精确匹配；3) 降低阈值（0.95-0.97）获得更多候选结果；4) 根据实际测试效果调整；5) 不同领域可能需要不同阈值。可通过配置文件或API动态调整。", "category": "tuning", "confidence": 0.9, "keywords": ["相似度阈值", "精确度", "召回率", "调优"], "source": "tuning_guide", "created_at": **********, "updated_at": **********}, {"id": "qa_014", "question": "问答系统如何处理多轮对话？", "answer": "当前版本的问答系统主要支持单轮问答，每次查询都是独立的。如需多轮对话功能，建议：1) 在应用层维护对话上下文；2) 将上下文信息作为查询的一部分；3) 使用会话ID管理对话状态；4) 结合主系统的对话管理功能。", "category": "conversation", "confidence": 0.8, "keywords": ["多轮对话", "上下文", "会话管理", "对话状态"], "source": "conversation_guide", "created_at": **********, "updated_at": **********}, {"id": "qa_015", "question": "如何扩展问答系统的功能？", "answer": "扩展问答系统功能的方法：1) 添加新的API端点；2) 扩展数据模型支持更多字段；3) 集成其他AI服务；4) 添加新的导入导出格式；5) 实现自定义相似度算法；6) 添加多语言支持；7) 集成外部知识源。系统采用模块化设计，便于扩展。", "category": "extension", "confidence": 0.85, "keywords": ["扩展", "功能增强", "模块化", "集成"], "source": "extension_guide", "created_at": **********, "updated_at": **********}]}