#!/usr/bin/env python3
"""
向量化Q&A系统完整功能示例

展示向量化问答对存储和检索的所有功能
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path

# 添加src路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qa_manager import QAManager
from embedding_client import create_embedding_client
from vectorized_qa_core import VectorizedQAStorage


async def example_1_basic_usage():
    """示例1：基础使用"""
    print("📝 示例1：基础使用")
    print("=" * 50)
    
    # 创建Q&A管理器
    qa_manager = QAManager(
        storage_file="example1_qa.json",
        similarity_threshold=0.80,
        max_results=3,
        use_mock_embedding=True  # 使用模拟embedding以确保示例运行
    )
    
    await qa_manager.initialize()
    print("✅ Q&A管理器初始化成功")
    
    # 添加问答对
    print("\n1️⃣ 添加问答对:")
    qa_pairs = [
        {
            "question": "什么是Python？",
            "answer": "Python是一种高级、解释型的编程语言，以其简洁性和可读性而闻名。",
            "category": "编程语言",
            "confidence": 0.98,
            "keywords": ["Python", "编程语言", "高级语言"]
        },
        {
            "question": "什么是JavaScript？",
            "answer": "JavaScript是一种动态编程语言，主要用于Web开发，可以在浏览器和服务器端运行。",
            "category": "编程语言",
            "confidence": 0.96,
            "keywords": ["JavaScript", "Web开发", "动态语言"]
        }
    ]
    
    for qa_data in qa_pairs:
        qa_id = await qa_manager.add_qa_pair(**qa_data)
        if qa_id:
            print(f"   ✅ 添加: {qa_data['question']}")
    
    # 查询测试
    print("\n2️⃣ 查询测试:")
    queries = ["Python编程语言", "JS是什么", "不相关的问题"]
    
    for query in queries:
        result = await qa_manager.query(query)
        print(f"\n   查询: {query}")
        if result["success"] and result["found"]:
            print(f"   ✅ 找到匹配 (相似度: {result['similarity']:.3f})")
            print(f"   💬 答案: {result['answer'][:60]}...")
        else:
            print(f"   ⚪ 未找到匹配")
    
    # 清理
    await qa_manager.cleanup()
    if os.path.exists("example1_qa.json"):
        os.remove("example1_qa.json")
    
    print("✅ 基础使用示例完成")


async def example_2_batch_operations():
    """示例2：批量操作"""
    print("\n📁 示例2：批量操作")
    print("=" * 50)
    
    # 创建示例数据文件
    sample_data = [
        {
            "question": "什么是Docker？",
            "answer": "Docker是一个开源的容器化平台，用于开发、部署和运行应用程序。",
            "category": "容器技术",
            "confidence": 0.95,
            "keywords": ["Docker", "容器", "部署"],
            "source": "tech_kb"
        },
        {
            "question": "什么是Kubernetes？",
            "answer": "Kubernetes是一个开源的容器编排平台，用于自动化容器的部署、扩展和管理。",
            "category": "容器技术",
            "confidence": 0.94,
            "keywords": ["Kubernetes", "容器编排", "自动化"],
            "source": "tech_kb"
        },
        {
            "question": "什么是微服务？",
            "answer": "微服务是一种软件架构模式，将应用程序构建为一组小型、独立的服务。",
            "category": "软件架构",
            "confidence": 0.92,
            "keywords": ["微服务", "软件架构", "独立服务"],
            "source": "tech_kb"
        },
        {
            "question": "什么是API？",
            "answer": "API（应用程序编程接口）是一组定义和协议，用于构建和集成应用软件。",
            "category": "软件开发",
            "confidence": 0.97,
            "keywords": ["API", "编程接口", "集成"],
            "source": "tech_kb"
        }
    ]
    
    # 保存到文件
    data_file = "example2_data.json"
    with open(data_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, indent=2, ensure_ascii=False)
    
    print(f"📁 创建示例数据文件: {data_file} ({len(sample_data)}个问答对)")
    
    # 创建Q&A管理器
    qa_manager = QAManager(
        storage_file="example2_qa.json",
        similarity_threshold=0.75,
        max_results=5,
        use_mock_embedding=True
    )
    
    await qa_manager.initialize()
    
    # 批量导入
    print("\n1️⃣ 批量导入:")
    import_success = await qa_manager.import_from_json(data_file)
    
    if import_success:
        print("   ✅ 批量导入成功")
        
        # 获取统计信息
        stats = qa_manager.get_statistics()
        print(f"   📊 导入统计:")
        print(f"      总问答对: {stats['storage_stats']['total_pairs']}")
        print(f"      分类数量: {len(stats['storage_stats']['categories'])}")
        print(f"      分类分布: {stats['storage_stats']['categories']}")
    
    # 分类查询
    print("\n2️⃣ 分类查询:")
    categories = qa_manager.get_statistics()['storage_stats']['categories']
    for category in categories:
        qa_pairs = qa_manager.list_qa_pairs(category=category)
        print(f"   {category}: {len(qa_pairs)} 个问答对")
        for qa in qa_pairs:
            print(f"      - {qa['question']}")
    
    # 批量查询测试
    print("\n3️⃣ 批量查询测试:")
    batch_queries = [
        "容器技术是什么？",
        "Kubernetes的作用",
        "微服务架构",
        "API接口",
        "不相关的查询"
    ]
    
    for query in batch_queries:
        result = await qa_manager.query(query, top_k=2)
        print(f"\n   查询: {query}")
        
        if result["success"] and result["found"]:
            print(f"   ✅ 最佳匹配 (相似度: {result['similarity']:.3f})")
            print(f"   📝 答案: {result['answer'][:50]}...")
            
            # 显示其他匹配
            if len(result["all_results"]) > 1:
                print(f"   📋 其他匹配:")
                for i, r in enumerate(result["all_results"][1:], 1):
                    print(f"      {i+1}. 相似度: {r['similarity']:.3f} - {r['question'][:30]}...")
        else:
            print(f"   ⚪ 未找到匹配")
    
    # 清理
    await qa_manager.cleanup()
    for file in [data_file, "example2_qa.json"]:
        if os.path.exists(file):
            os.remove(file)
    
    print("✅ 批量操作示例完成")


async def example_3_advanced_search():
    """示例3：高级搜索功能"""
    print("\n🔍 示例3：高级搜索功能")
    print("=" * 50)
    
    # 创建存储实例进行底层操作
    from embedding_client import MockEmbeddingClient
    
    embedding_client = MockEmbeddingClient(embedding_dim=2560)
    storage = VectorizedQAStorage(
        storage_file="example3_storage.json",
        embedding_func=embedding_client,
        similarity_threshold=0.70,
        max_results=10
    )
    
    await storage.initialize()
    print("✅ 向量化存储初始化成功")
    
    # 添加测试数据
    print("\n1️⃣ 添加测试数据:")
    test_data = [
        {
            "question": "什么是机器学习算法？",
            "answer": "机器学习算法是一套数学方法，使计算机能够从数据中学习模式。",
            "category": "机器学习",
            "confidence": 0.95
        },
        {
            "question": "什么是深度学习算法？",
            "answer": "深度学习算法使用多层神经网络来学习数据的复杂表示。",
            "category": "深度学习",
            "confidence": 0.94
        },
        {
            "question": "什么是监督学习？",
            "answer": "监督学习是一种机器学习方法，使用标记的训练数据来学习映射函数。",
            "category": "机器学习",
            "confidence": 0.93
        },
        {
            "question": "什么是无监督学习？",
            "answer": "无监督学习是在没有标记数据的情况下发现数据中隐藏模式的方法。",
            "category": "机器学习",
            "confidence": 0.92
        },
        {
            "question": "什么是强化学习？",
            "answer": "强化学习是通过与环境交互来学习最优行为策略的机器学习方法。",
            "category": "强化学习",
            "confidence": 0.91
        }
    ]
    
    added_ids = await storage.add_qa_pairs_batch(test_data)
    print(f"   ✅ 添加了 {len(added_ids)} 个问答对")
    
    # 测试不同相似度阈值的搜索
    print("\n2️⃣ 不同相似度阈值搜索:")
    query = "机器学习方法"
    thresholds = [0.95, 0.85, 0.75, 0.65]
    
    for threshold in thresholds:
        results = await storage.search_similar_questions(
            query, 
            top_k=5,
            min_similarity=threshold
        )
        print(f"\n   阈值 {threshold}: 找到 {len(results)} 个结果")
        for i, result in enumerate(results, 1):
            print(f"      {i}. 相似度: {result.similarity:.3f} - {result.qa_pair.question}")
    
    # 测试Top-K搜索
    print("\n3️⃣ Top-K搜索:")
    query = "学习算法"
    for k in [1, 3, 5]:
        results = await storage.search_similar_questions(query, top_k=k, min_similarity=0.60)
        print(f"\n   Top-{k} 结果:")
        for i, result in enumerate(results, 1):
            print(f"      {i}. 相似度: {result.similarity:.3f} - {result.qa_pair.question}")
    
    # 测试最佳匹配
    print("\n4️⃣ 最佳匹配:")
    queries = ["深度学习", "监督学习", "强化学习", "不相关查询"]
    
    for query in queries:
        best_match = await storage.get_best_match(query)
        print(f"\n   查询: {query}")
        if best_match:
            print(f"   ✅ 最佳匹配: {best_match.qa_pair.question}")
            print(f"   🎯 相似度: {best_match.similarity:.3f}")
            print(f"   📝 答案: {best_match.qa_pair.answer[:50]}...")
        else:
            print(f"   ⚪ 未找到匹配")
    
    # 获取详细统计
    print("\n5️⃣ 详细统计:")
    stats = storage.get_statistics()
    print(f"   总问答对: {stats['total_pairs']}")
    print(f"   分类分布: {stats['categories']}")
    print(f"   向量维度: {stats['embedding_dim']}")
    print(f"   查询统计: {stats['query_stats']}")
    
    # 清理
    await storage.cleanup()
    if os.path.exists("example3_storage.json"):
        os.remove("example3_storage.json")
    
    print("✅ 高级搜索示例完成")


async def example_4_performance_analysis():
    """示例4：性能分析"""
    print("\n⚡ 示例4：性能分析")
    print("=" * 50)
    
    # 创建Q&A管理器
    qa_manager = QAManager(
        storage_file="example4_qa.json",
        similarity_threshold=0.75,
        max_results=5,
        use_mock_embedding=True
    )
    
    await qa_manager.initialize()
    
    # 创建性能测试数据
    print("\n1️⃣ 创建性能测试数据:")
    performance_data = []
    for i in range(30):
        performance_data.append({
            "question": f"性能测试问题 {i}: 这是关于主题{i%6}的详细问题",
            "answer": f"这是问题{i}的详细答案，包含关于主题{i%6}的完整信息。",
            "category": f"分类{i%4}",
            "confidence": 0.8 + (i % 20) * 0.01,
            "keywords": [f"关键词{i}", f"主题{i%6}"],
            "source": "performance_test"
        })
    
    # 测试批量添加性能
    print(f"   📊 准备添加 {len(performance_data)} 个问答对...")
    start_time = time.time()
    added_ids = await qa_manager.add_qa_pairs_batch(performance_data)
    add_time = time.time() - start_time
    
    print(f"   ✅ 批量添加完成:")
    print(f"      - 总耗时: {add_time:.3f}秒")
    print(f"      - 平均每个: {add_time/len(performance_data)*1000:.2f}毫秒")
    print(f"      - 成功添加: {len(added_ids)} 个")
    
    # 测试查询性能
    print("\n2️⃣ 查询性能测试:")
    test_queries = [
        "性能测试问题 5",
        "关于主题2的问题",
        "分类1的内容",
        "主题4相关",
        "完全不相关的查询"
    ]
    
    query_times = []
    successful_queries = 0
    
    for query in test_queries:
        start_time = time.time()
        result = await qa_manager.query(query)
        query_time = time.time() - start_time
        query_times.append(query_time)
        
        if result["success"]:
            successful_queries += 1
            if result["found"]:
                print(f"   ✅ '{query}' -> 命中 ({query_time*1000:.2f}ms, 相似度: {result['similarity']:.3f})")
            else:
                print(f"   ⚪ '{query}' -> 未命中 ({query_time*1000:.2f}ms)")
        else:
            print(f"   ❌ '{query}' -> 错误 ({query_time*1000:.2f}ms)")
    
    # 性能统计
    avg_query_time = sum(query_times) / len(query_times)
    print(f"\n3️⃣ 性能统计:")
    print(f"   查询性能:")
    print(f"      - 平均查询时间: {avg_query_time*1000:.2f}毫秒")
    print(f"      - 最快查询: {min(query_times)*1000:.2f}毫秒")
    print(f"      - 最慢查询: {max(query_times)*1000:.2f}毫秒")
    print(f"      - 成功查询率: {successful_queries/len(test_queries)*100:.1f}%")
    
    # 获取系统统计
    stats = qa_manager.get_statistics()
    print(f"   系统统计:")
    print(f"      - 总问答对: {stats['storage_stats']['total_pairs']}")
    print(f"      - 向量索引大小: {stats['storage_stats']['vector_index_size']}")
    print(f"      - 向量维度: {stats['storage_stats']['embedding_dim']}")
    print(f"      - 总查询数: {stats['query_performance']['total_queries']}")
    print(f"      - 平均响应时间: {stats['query_performance']['avg_response_time_ms']:.2f}ms")
    
    # Embedding性能
    if 'embedding_stats' in stats:
        embedding_stats = stats['embedding_stats']['stats']
        print(f"   Embedding统计:")
        print(f"      - 总请求: {embedding_stats['total_requests']}")
        print(f"      - 总文本: {embedding_stats['total_texts']}")
        if embedding_stats['total_requests'] > 0:
            print(f"      - 平均每请求文本数: {embedding_stats['total_texts']/embedding_stats['total_requests']:.1f}")
    
    # 内存使用分析
    print("\n4️⃣ 内存使用分析:")
    import sys
    
    # 估算向量存储大小
    vector_count = stats['storage_stats']['vector_index_size']
    vector_dim = stats['storage_stats']['embedding_dim']
    estimated_vector_size = vector_count * vector_dim * 4  # 假设float32
    
    print(f"   向量存储:")
    print(f"      - 向量数量: {vector_count}")
    print(f"      - 向量维度: {vector_dim}")
    print(f"      - 估算大小: {estimated_vector_size/1024/1024:.2f} MB")
    
    # 清理
    await qa_manager.cleanup()
    if os.path.exists("example4_qa.json"):
        os.remove("example4_qa.json")
    
    print("✅ 性能分析示例完成")


async def example_5_real_embedding():
    """示例5：真实embedding模型使用"""
    print("\n🌐 示例5：真实embedding模型使用")
    print("=" * 50)

    # 尝试使用真实embedding
    try:
        embedding_client = create_embedding_client(use_mock=False)
        connection_ok = await embedding_client.test_connection()

        if not connection_ok:
            print("❌ 真实embedding连接失败，跳过此示例")
            return

        print("✅ 真实embedding连接成功")

        # 创建Q&A管理器
        qa_manager = QAManager(
            storage_file="example5_qa.json",
            embedding_client=embedding_client,
            similarity_threshold=0.85,
            max_results=3
        )

        await qa_manager.initialize()

        # 添加一些技术问答
        print("\n1️⃣ 添加技术问答:")
        tech_qa = [
            {
                "question": "什么是Transformer架构？",
                "answer": "Transformer是一种基于注意力机制的神经网络架构，广泛应用于自然语言处理任务。",
                "category": "深度学习",
                "confidence": 0.96
            },
            {
                "question": "什么是BERT模型？",
                "answer": "BERT是一种双向编码器表示的Transformer模型，在多个NLP任务上取得了突破性成果。",
                "category": "深度学习",
                "confidence": 0.95
            }
        ]

        for qa_data in tech_qa:
            qa_id = await qa_manager.add_qa_pair(**qa_data)
            if qa_id:
                print(f"   ✅ 添加: {qa_data['question']}")

        # 测试真实embedding的查询效果
        print("\n2️⃣ 真实embedding查询测试:")
        queries = [
            "Transformer模型是什么？",
            "BERT的原理",
            "注意力机制",
            "不相关的问题"
        ]

        for query in queries:
            result = await qa_manager.query(query)
            print(f"\n   查询: {query}")
            if result["success"] and result["found"]:
                print(f"   ✅ 找到匹配 (相似度: {result['similarity']:.3f})")
                print(f"   💬 答案: {result['answer'][:60]}...")
            else:
                print(f"   ⚪ 未找到匹配")

        # 获取embedding统计
        print("\n3️⃣ Embedding统计:")
        stats = qa_manager.get_statistics()
        if 'embedding_stats' in stats:
            embedding_stats = stats['embedding_stats']
            print(f"   配置: {embedding_stats['config']}")
            print(f"   统计: {embedding_stats['stats']}")

        # 清理
        await qa_manager.cleanup()
        if os.path.exists("example5_qa.json"):
            os.remove("example5_qa.json")

        print("✅ 真实embedding示例完成")

    except Exception as e:
        print(f"❌ 真实embedding示例失败: {e}")


async def main():
    """主函数"""
    print("🎯 向量化Q&A系统完整功能示例")
    print("=" * 70)

    examples = [
        ("基础使用", example_1_basic_usage),
        ("批量操作", example_2_batch_operations),
        ("高级搜索功能", example_3_advanced_search),
        ("性能分析", example_4_performance_analysis),
        ("真实embedding模型", example_5_real_embedding)
    ]

    for example_name, example_func in examples:
        print(f"\n{'='*25} {example_name} {'='*25}")
        try:
            await example_func()
        except Exception as e:
            print(f"❌ {example_name}示例失败: {e}")
            import traceback
            traceback.print_exc()

    print(f"\n{'='*70}")
    print("🎉 所有向量化Q&A功能示例运行完成！")
    print("\n📚 功能总结:")
    print("   ✅ 问题自动向量化存储")
    print("   ✅ 答案文本格式保存")
    print("   ✅ 向量化问题和答案一同存放")
    print("   ✅ 基于相似度的智能搜索")
    print("   ✅ 批量数据导入导出")
    print("   ✅ 高级搜索和过滤")
    print("   ✅ 性能监控和分析")
    print("   ✅ 真实embedding模型集成")
    print("   ✅ 灵活的配置选项")
    print("\n🚀 向量化Q&A系统已准备好用于生产环境！")


if __name__ == "__main__":
    asyncio.run(main())
