#!/usr/bin/env python3
"""
向量化Q&A系统完整测试

测试向量化问答对存储和检索的所有功能
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path

# 添加src路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from vectorized_qa_core import VectorizedQAStorage, QAPair, SearchResult
from embedding_client import EmbeddingClient, MockEmbeddingClient, create_embedding_client
from qa_manager import QAManager


async def test_embedding_client():
    """测试Embedding客户端"""
    print("🧪 测试Embedding客户端")
    print("=" * 50)
    
    # 测试真实embedding客户端
    print("\n1️⃣ 测试真实Embedding客户端:")
    try:
        real_client = EmbeddingClient()
        
        # 测试连接
        connection_ok = await real_client.test_connection()
        if connection_ok:
            print("   ✅ 真实embedding客户端连接成功")
            
            # 测试单个文本向量化
            test_text = "什么是人工智能？"
            embedding = await real_client.embed_single_text(test_text)
            print(f"   ✅ 单文本向量化成功，维度: {len(embedding)}")
            
            # 测试批量向量化
            test_texts = ["什么是机器学习？", "什么是深度学习？", "什么是自然语言处理？"]
            embeddings = await real_client.embed_texts(test_texts)
            print(f"   ✅ 批量向量化成功，{len(embeddings)} 个文本，维度: {len(embeddings[0])}")
            
            # 获取统计信息
            stats = real_client.get_statistics()
            print(f"   📊 统计信息: {stats['stats']}")
            
            return True, real_client
        else:
            print("   ❌ 真实embedding客户端连接失败")
            
    except Exception as e:
        print(f"   ❌ 真实embedding客户端测试失败: {e}")
    
    # 回退到模拟客户端
    print("\n2️⃣ 使用模拟Embedding客户端:")
    mock_client = MockEmbeddingClient(embedding_dim=2560)
    
    # 测试模拟客户端
    test_texts = ["测试文本1", "测试文本2"]
    embeddings = await mock_client.embed_texts(test_texts)
    print(f"   ✅ 模拟向量化成功，{len(embeddings)} 个文本，维度: {len(embeddings[0])}")
    
    return False, mock_client


async def test_vectorized_storage(embedding_client):
    """测试向量化存储"""
    print("\n🧪 测试向量化存储")
    print("=" * 50)
    
    # 创建存储实例
    storage = VectorizedQAStorage(
        storage_file="test_storage.json",
        embedding_func=embedding_client,
        similarity_threshold=0.75,
        max_results=5
    )
    
    # 初始化
    success = await storage.initialize()
    if not success:
        print("❌ 存储初始化失败")
        return False
    
    print("✅ 存储初始化成功")
    
    # 测试添加单个问答对
    print("\n1️⃣ 测试添加单个问答对:")
    qa_id = await storage.add_qa_pair(
        question="什么是向量化？",
        answer="向量化是将文本转换为数值向量的过程，用于机器学习和相似度计算。",
        category="技术",
        confidence=0.95,
        keywords=["向量化", "文本处理", "机器学习"],
        source="test"
    )
    
    if qa_id:
        print(f"   ✅ 成功添加问答对: {qa_id}")
    else:
        print("   ❌ 添加问答对失败")
        return False
    
    # 测试批量添加
    print("\n2️⃣ 测试批量添加:")
    batch_data = [
        {
            "question": "什么是人工智能？",
            "answer": "人工智能（AI）是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的智能机器。",
            "category": "AI基础",
            "confidence": 0.98,
            "keywords": ["AI", "人工智能", "机器智能"]
        },
        {
            "question": "什么是机器学习？",
            "answer": "机器学习是AI的一个子集，它使计算机能够从数据中学习和改进，而无需明确编程。",
            "category": "机器学习",
            "confidence": 0.95,
            "keywords": ["机器学习", "ML", "数据学习"]
        },
        {
            "question": "什么是深度学习？",
            "answer": "深度学习是机器学习的一个子集，使用具有多层的神经网络来建模和理解复杂数据模式。",
            "category": "深度学习",
            "confidence": 0.94,
            "keywords": ["深度学习", "神经网络", "多层网络"]
        }
    ]
    
    added_ids = await storage.add_qa_pairs_batch(batch_data)
    print(f"   ✅ 批量添加成功: {len(added_ids)} 个问答对")
    
    # 测试搜索
    print("\n3️⃣ 测试相似度搜索:")
    search_queries = [
        "向量化是什么？",
        "AI的定义",
        "机器学习原理",
        "深度学习技术",
        "完全不相关的问题"
    ]
    
    for query in search_queries:
        results = await storage.search_similar_questions(query, top_k=3)
        print(f"\n   查询: {query}")
        if results:
            for i, result in enumerate(results, 1):
                print(f"      {i}. 相似度: {result.similarity:.3f} - {result.qa_pair.question}")
        else:
            print(f"      未找到匹配结果")
    
    # 测试最佳匹配
    print("\n4️⃣ 测试最佳匹配:")
    best_match = await storage.get_best_match("人工智能是什么？")
    if best_match:
        print(f"   ✅ 最佳匹配: {best_match.qa_pair.question}")
        print(f"   📝 答案: {best_match.qa_pair.answer[:50]}...")
        print(f"   🎯 相似度: {best_match.similarity:.3f}")
    else:
        print("   ⚪ 未找到最佳匹配")
    
    # 测试保存和加载
    print("\n5️⃣ 测试保存和加载:")
    save_success = await storage.save_to_file()
    if save_success:
        print("   ✅ 保存成功")
        
        # 创建新实例并加载
        new_storage = VectorizedQAStorage(
            storage_file="test_storage.json",
            embedding_func=embedding_client,
            similarity_threshold=0.75,
            max_results=5
        )
        
        await new_storage.initialize()
        load_success = await new_storage.load_from_file()
        if load_success:
            print(f"   ✅ 加载成功: {len(new_storage.qa_pairs)} 个问答对")
        else:
            print("   ❌ 加载失败")
    
    # 获取统计信息
    print("\n6️⃣ 统计信息:")
    stats = storage.get_statistics()
    print(f"   总问答对: {stats['total_pairs']}")
    print(f"   分类分布: {stats['categories']}")
    print(f"   向量维度: {stats['embedding_dim']}")
    print(f"   查询统计: {stats['query_stats']}")
    
    # 清理
    await storage.cleanup()
    if os.path.exists("test_storage.json"):
        os.remove("test_storage.json")
    
    print("✅ 向量化存储测试完成")
    return True


async def test_qa_manager(use_real_embedding: bool, embedding_client):
    """测试Q&A管理器"""
    print("\n🧪 测试Q&A管理器")
    print("=" * 50)
    
    # 创建Q&A管理器
    qa_manager = QAManager(
        storage_file="test_qa_manager.json",
        embedding_client=embedding_client,
        similarity_threshold=0.80,
        max_results=3,
        use_mock_embedding=not use_real_embedding
    )
    
    # 初始化
    success = await qa_manager.initialize()
    if not success:
        print("❌ Q&A管理器初始化失败")
        return False
    
    print("✅ Q&A管理器初始化成功")
    
    # 测试添加问答对
    print("\n1️⃣ 测试添加问答对:")
    qa_id = await qa_manager.add_qa_pair(
        question="什么是自然语言处理？",
        answer="自然语言处理（NLP）是AI的一个分支，专注于使计算机能够理解、解释和生成人类语言。",
        category="NLP",
        confidence=0.93,
        keywords=["NLP", "自然语言处理", "文本分析"]
    )
    
    if qa_id:
        print(f"   ✅ 添加成功: {qa_id}")
    
    # 创建测试数据文件
    test_data = [
        {
            "question": "什么是计算机视觉？",
            "answer": "计算机视觉是使计算机能够理解和解释视觉信息的技术，包括图像识别、物体检测等。",
            "category": "计算机视觉",
            "confidence": 0.91,
            "keywords": ["计算机视觉", "图像处理", "CV"]
        },
        {
            "question": "什么是强化学习？",
            "answer": "强化学习是机器学习的一个分支，通过与环境交互来学习最优行为策略。",
            "category": "强化学习",
            "confidence": 0.89,
            "keywords": ["强化学习", "RL", "策略学习"]
        },
        {
            "question": "什么是迁移学习？",
            "answer": "迁移学习是一种机器学习技术，将在一个任务上学到的知识应用到相关的新任务上。",
            "category": "迁移学习",
            "confidence": 0.87,
            "keywords": ["迁移学习", "知识迁移", "预训练"]
        }
    ]
    
    test_file = "test_qa_data.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2, ensure_ascii=False)
    
    # 测试批量导入
    print("\n2️⃣ 测试批量导入:")
    import_success = await qa_manager.import_from_json(test_file)
    if import_success:
        print("   ✅ 批量导入成功")
        
        # 获取统计信息
        stats = qa_manager.get_statistics()
        print(f"   📊 总问答对: {stats['storage_stats']['total_pairs']}")
        print(f"   📊 分类分布: {stats['storage_stats']['categories']}")
    
    # 测试查询
    print("\n3️⃣ 测试智能查询:")
    test_queries = [
        "NLP是什么？",
        "计算机视觉技术",
        "强化学习算法",
        "知识迁移方法",
        "完全不相关的问题"
    ]
    
    successful_queries = 0
    total_queries = len(test_queries)
    
    for query in test_queries:
        result = await qa_manager.query(query, top_k=2)
        print(f"\n   查询: {query}")
        
        if result["success"]:
            if result["found"]:
                successful_queries += 1
                similarity = result["similarity"]
                answer = result["answer"]
                category = result["category"]
                
                print(f"      ✅ 找到匹配 (相似度: {similarity:.3f})")
                print(f"      🏷️ 分类: {category}")
                print(f"      💬 答案: {answer[:60]}...")
                
                # 显示其他匹配结果
                if len(result["all_results"]) > 1:
                    print(f"      📋 其他匹配:")
                    for i, r in enumerate(result["all_results"][1:], 1):
                        print(f"         {i+1}. 相似度: {r['similarity']:.3f} - {r['question'][:35]}...")
            else:
                print(f"      ⚪ 未找到匹配: {result['message']}")
        else:
            print(f"      ❌ 查询失败: {result['error']}")
    
    # 测试列出问答对
    print("\n4️⃣ 测试列出问答对:")
    all_qa_pairs = qa_manager.list_qa_pairs()
    print(f"   📋 总共 {len(all_qa_pairs)} 个问答对:")
    
    categories = set(qa['category'] for qa in all_qa_pairs)
    for category in categories:
        category_qa = qa_manager.list_qa_pairs(category=category)
        print(f"      {category}: {len(category_qa)} 个")
    
    # 获取最终统计
    print("\n5️⃣ 最终统计:")
    final_stats = qa_manager.get_statistics()
    
    print(f"   存储统计:")
    print(f"      - 总问答对: {final_stats['storage_stats']['total_pairs']}")
    print(f"      - 向量维度: {final_stats['storage_stats']['embedding_dim']}")
    print(f"      - 向量索引大小: {final_stats['storage_stats']['vector_index_size']}")
    
    print(f"   查询性能:")
    print(f"      - 总查询: {final_stats['query_performance']['total_queries']}")
    print(f"      - 成功查询: {final_stats['query_performance']['successful_queries']}")
    print(f"      - 成功率: {final_stats['query_performance']['success_rate_percent']:.1f}%")
    print(f"      - 平均响应时间: {final_stats['query_performance']['avg_response_time_ms']:.2f}ms")
    
    if 'embedding_stats' in final_stats:
        print(f"   Embedding统计:")
        embedding_stats = final_stats['embedding_stats']['stats']
        print(f"      - 总请求: {embedding_stats['total_requests']}")
        print(f"      - 成功请求: {embedding_stats.get('successful_requests', 'N/A')}")
        print(f"      - 总文本: {embedding_stats['total_texts']}")
    
    print(f"\n   测试结果:")
    print(f"      - 查询成功率: {successful_queries}/{total_queries} ({successful_queries/total_queries*100:.1f}%)")
    
    # 清理
    await qa_manager.cleanup()
    for file in [test_file, "test_qa_manager.json"]:
        if os.path.exists(file):
            os.remove(file)
    
    print("✅ Q&A管理器测试完成")
    return True


async def test_performance(embedding_client):
    """测试性能"""
    print("\n🧪 测试性能")
    print("=" * 50)
    
    # 创建大量测试数据
    large_data = []
    for i in range(20):  # 减少数量以适应测试
        large_data.append({
            "question": f"测试问题 {i}: 这是一个关于主题{i%5}的详细问题",
            "answer": f"这是问题{i}的详细答案，包含了关于主题{i%5}的完整信息和解释。",
            "category": f"分类{i%3}",
            "confidence": 0.8 + (i % 20) * 0.01,
            "keywords": [f"关键词{i}", f"主题{i%5}"],
            "source": "performance_test"
        })
    
    # 保存测试数据
    test_file = "performance_test_qa.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(large_data, f, indent=2, ensure_ascii=False)
    
    # 创建管理器
    qa_manager = QAManager(
        storage_file="test_performance_qa.json",
        embedding_client=embedding_client,
        similarity_threshold=0.70,
        max_results=5
    )
    
    await qa_manager.initialize()
    
    # 测试导入性能
    print("📁 测试批量导入性能...")
    start_time = time.time()
    success = await qa_manager.import_from_json(test_file)
    import_time = time.time() - start_time
    
    if success:
        print(f"✅ 导入{len(large_data)}个问答对耗时: {import_time:.3f}秒")
        print(f"   平均每个问答对: {import_time/len(large_data)*1000:.2f}毫秒")
    
    # 测试查询性能
    print("\n🔍 测试查询性能...")
    test_queries = [
        "测试问题 5",
        "关于主题2的问题",
        "分类1的内容",
        "完全不相关的查询"
    ]
    
    total_query_time = 0
    successful_queries = 0
    
    for query in test_queries:
        start_time = time.time()
        result = await qa_manager.query(query)
        query_time = time.time() - start_time
        total_query_time += query_time
        
        if result["success"]:
            successful_queries += 1
            if result["found"]:
                print(f"   ✅ '{query}' -> 命中 ({query_time*1000:.2f}ms, 相似度: {result['similarity']:.3f})")
            else:
                print(f"   ⚪ '{query}' -> 未命中 ({query_time*1000:.2f}ms)")
        else:
            print(f"   ❌ '{query}' -> 错误 ({query_time*1000:.2f}ms)")
    
    avg_query_time = total_query_time / len(test_queries)
    print(f"\n📊 查询性能统计:")
    print(f"   平均查询时间: {avg_query_time*1000:.2f}毫秒")
    print(f"   成功查询率: {successful_queries/len(test_queries)*100:.1f}%")
    
    # 获取详细统计
    stats = qa_manager.get_statistics()
    print(f"\n📈 系统统计:")
    print(f"   总问答对: {stats['storage_stats']['total_pairs']}")
    print(f"   向量索引大小: {stats['storage_stats']['vector_index_size']}")
    print(f"   向量维度: {stats['storage_stats']['embedding_dim']}")
    
    # 清理文件
    await qa_manager.cleanup()
    for file in [test_file, "test_performance_qa.json"]:
        if os.path.exists(file):
            os.remove(file)
    
    print("✅ 性能测试完成")
    return True


async def main():
    """主测试函数"""
    print("🎯 向量化Q&A系统完整测试")
    print("=" * 70)
    
    # 测试embedding客户端
    use_real_embedding, embedding_client = await test_embedding_client()
    
    if use_real_embedding:
        print("\n🚀 使用真实embedding模型进行测试")
    else:
        print("\n🔧 使用模拟embedding进行测试")
    
    # 运行所有测试
    test_functions = [
        ("向量化存储", lambda: test_vectorized_storage(embedding_client)),
        ("Q&A管理器", lambda: test_qa_manager(use_real_embedding, embedding_client)),
        ("性能测试", lambda: test_performance(embedding_client))
    ]
    
    results = []
    
    for test_name, test_func in test_functions:
        print(f"\n{'='*25} {test_name} {'='*25}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n{'='*70}")
    print("🏁 测试结果汇总")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有向量化Q&A功能测试通过！")
        print("\n📋 已验证功能:")
        print("   ✅ 问题自动向量化存储")
        print("   ✅ 答案文本格式保存")
        print("   ✅ 向量化问题和答案一同存放")
        print("   ✅ 高效相似度搜索")
        print("   ✅ 批量导入导出")
        print("   ✅ 智能Q&A管理")
        print("   ✅ 性能监控统计")
        if use_real_embedding:
            print("   ✅ 真实embedding模型集成")
        print("\n🚀 向量化Q&A系统功能完全正常！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
