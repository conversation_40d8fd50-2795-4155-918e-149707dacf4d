import{j as t,E as Wa,I as Dt,F as Ga,G as Va,H as Nt,J as Ya,V as zt,L as Ja,K as Qa,M as Ct,N as Pt,Q as Xa,U as St,W as Et,X as _t,_ as ge,d as Ft}from"./ui-vendor-CeCm8EER.js";import{r as o,g as Za,R as Tt}from"./react-vendor-DEwriMA6.js";import{c as E,C as et,a as At,b as Ot,d as oa,F as Rt,e as sa,f as at,u as me,s as Mt,g as O,U as la,S as It,h as tt,B as T,X as nt,i as qt,j as ae,D as qe,k as ha,l as Le,m as Be,n as Ue,o as $e,p as Lt,q as Bt,E as Ut,T as it,I as Oe,r as ot,t as st,L as $t,v as Ht,w as Kt,x as ba,y as ya,z as Wt,A as Gt,G as Vt,H as Yt,J as Jt,K as Qt,M as Ce,N as Pe,O as ja,P as wa,Q as Xt,R as ka,V as Da,W as Zt,Y as en,Z as an,_ as Qe,$ as Xe}from"./feature-graph-DtzGBYZt.js";const Na=St,yi=_t,za=Et,ra=o.forwardRef(({className:e,children:a,...n},i)=>t.jsxs(Wa,{ref:i,className:E("border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-10 w-full items-center justify-between rounded-md border px-3 py-2 text-sm focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[a,t.jsx(Dt,{asChild:!0,children:t.jsx(et,{className:"h-4 w-4 opacity-50"})})]}));ra.displayName=Wa.displayName;const lt=o.forwardRef(({className:e,...a},n)=>t.jsx(Ga,{ref:n,className:E("flex cursor-default items-center justify-center py-1",e),...a,children:t.jsx(At,{className:"h-4 w-4"})}));lt.displayName=Ga.displayName;const rt=o.forwardRef(({className:e,...a},n)=>t.jsx(Va,{ref:n,className:E("flex cursor-default items-center justify-center py-1",e),...a,children:t.jsx(et,{className:"h-4 w-4"})}));rt.displayName=Va.displayName;const ca=o.forwardRef(({className:e,children:a,position:n="popper",...i},l)=>t.jsx(Nt,{children:t.jsxs(Ya,{ref:l,className:E("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...i,children:[t.jsx(lt,{}),t.jsx(zt,{className:E("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),t.jsx(rt,{})]})}));ca.displayName=Ya.displayName;const tn=o.forwardRef(({className:e,...a},n)=>t.jsx(Ja,{ref:n,className:E("py-1.5 pr-2 pl-8 text-sm font-semibold",e),...a}));tn.displayName=Ja.displayName;const pa=o.forwardRef(({className:e,children:a,...n},i)=>t.jsxs(Qa,{ref:i,className:E("focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-default items-center rounded-sm py-1.5 pr-2 pl-8 text-sm outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(Ct,{children:t.jsx(Ot,{className:"h-4 w-4"})})}),t.jsx(Pt,{children:a})]}));pa.displayName=Qa.displayName;const nn=o.forwardRef(({className:e,...a},n)=>t.jsx(Xa,{ref:n,className:E("bg-muted -mx-1 my-1 h-px",e),...a}));nn.displayName=Xa.displayName;const ct=o.forwardRef(({className:e,...a},n)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:n,className:E("w-full caption-bottom text-sm",e),...a})}));ct.displayName="Table";const pt=o.forwardRef(({className:e,...a},n)=>t.jsx("thead",{ref:n,className:E("[&_tr]:border-b",e),...a}));pt.displayName="TableHeader";const dt=o.forwardRef(({className:e,...a},n)=>t.jsx("tbody",{ref:n,className:E("[&_tr:last-child]:border-0",e),...a}));dt.displayName="TableBody";const on=o.forwardRef(({className:e,...a},n)=>t.jsx("tfoot",{ref:n,className:E("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0",e),...a}));on.displayName="TableFooter";const da=o.forwardRef(({className:e,...a},n)=>t.jsx("tr",{ref:n,className:E("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...a}));da.displayName="TableRow";const le=o.forwardRef(({className:e,...a},n)=>t.jsx("th",{ref:n,className:E("text-muted-foreground h-10 px-2 text-left align-middle font-medium [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a}));le.displayName="TableHead";const re=o.forwardRef(({className:e,...a},n)=>t.jsx("td",{ref:n,className:E("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a}));re.displayName="TableCell";const sn=o.forwardRef(({className:e,...a},n)=>t.jsx("caption",{ref:n,className:E("text-muted-foreground mt-4 text-sm",e),...a}));sn.displayName="TableCaption";function ln({title:e,description:a,icon:n=Rt,action:i,className:l,...r}){return t.jsxs(oa,{className:E("flex w-full flex-col items-center justify-center space-y-6 bg-transparent p-16",l),...r,children:[t.jsx("div",{className:"mr-4 shrink-0 rounded-full border border-dashed p-4",children:t.jsx(n,{className:"text-muted-foreground size-8","aria-hidden":"true"})}),t.jsxs("div",{className:"flex flex-col items-center gap-1.5 text-center",children:[t.jsx(sa,{children:e}),a?t.jsx(at,{children:a}):null]}),i||null]})}var Ze={exports:{}},ea,Ca;function rn(){if(Ca)return ea;Ca=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return ea=e,ea}var aa,Pa;function cn(){if(Pa)return aa;Pa=1;var e=rn();function a(){}function n(){}return n.resetWarningCache=a,aa=function(){function i(p,d,b,f,x,_){if(_!==e){var y=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw y.name="Invariant Violation",y}}i.isRequired=i;function l(){return i}var r={array:i,bigint:i,bool:i,func:i,number:i,object:i,string:i,symbol:i,any:i,arrayOf:l,element:i,elementType:i,instanceOf:l,node:i,objectOf:l,oneOf:l,oneOfType:l,shape:l,exact:l,checkPropTypes:n,resetWarningCache:a};return r.PropTypes=r,r},aa}var Sa;function pn(){return Sa||(Sa=1,Ze.exports=cn()()),Ze.exports}var dn=pn();const F=Za(dn),mn=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function ke(e,a,n){const i=un(e),{webkitRelativePath:l}=e,r=typeof a=="string"?a:typeof l=="string"&&l.length>0?l:`./${e.name}`;return typeof i.path!="string"&&Ea(i,"path",r),Ea(i,"relativePath",r),i}function un(e){const{name:a}=e;if(a&&a.lastIndexOf(".")!==-1&&!e.type){const i=a.split(".").pop().toLowerCase(),l=mn.get(i);l&&Object.defineProperty(e,"type",{value:l,writable:!1,configurable:!1,enumerable:!0})}return e}function Ea(e,a,n){Object.defineProperty(e,a,{value:n,writable:!1,configurable:!1,enumerable:!0})}const fn=[".DS_Store","Thumbs.db"];function xn(e){return ge(this,void 0,void 0,function*(){return Re(e)&&vn(e.dataTransfer)?yn(e.dataTransfer,e.type):gn(e)?hn(e):Array.isArray(e)&&e.every(a=>"getFile"in a&&typeof a.getFile=="function")?bn(e):[]})}function vn(e){return Re(e)}function gn(e){return Re(e)&&Re(e.target)}function Re(e){return typeof e=="object"&&e!==null}function hn(e){return ma(e.target.files).map(a=>ke(a))}function bn(e){return ge(this,void 0,void 0,function*(){return(yield Promise.all(e.map(n=>n.getFile()))).map(n=>ke(n))})}function yn(e,a){return ge(this,void 0,void 0,function*(){if(e.items){const n=ma(e.items).filter(l=>l.kind==="file");if(a!=="drop")return n;const i=yield Promise.all(n.map(jn));return _a(mt(i))}return _a(ma(e.files).map(n=>ke(n)))})}function _a(e){return e.filter(a=>fn.indexOf(a.name)===-1)}function ma(e){if(e===null)return[];const a=[];for(let n=0;n<e.length;n++){const i=e[n];a.push(i)}return a}function jn(e){if(typeof e.webkitGetAsEntry!="function")return Fa(e);const a=e.webkitGetAsEntry();return a&&a.isDirectory?ut(a):Fa(e,a)}function mt(e){return e.reduce((a,n)=>[...a,...Array.isArray(n)?mt(n):[n]],[])}function Fa(e,a){return ge(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const r=yield e.getAsFileSystemHandle();if(r===null)throw new Error(`${e} is not a File`);if(r!==void 0){const p=yield r.getFile();return p.handle=r,ke(p)}}const i=e.getAsFile();if(!i)throw new Error(`${e} is not a File`);return ke(i,(n=a==null?void 0:a.fullPath)!==null&&n!==void 0?n:void 0)})}function wn(e){return ge(this,void 0,void 0,function*(){return e.isDirectory?ut(e):kn(e)})}function ut(e){const a=e.createReader();return new Promise((n,i)=>{const l=[];function r(){a.readEntries(p=>ge(this,void 0,void 0,function*(){if(p.length){const d=Promise.all(p.map(wn));l.push(d),r()}else try{const d=yield Promise.all(l);n(d)}catch(d){i(d)}}),p=>{i(p)})}r()})}function kn(e){return ge(this,void 0,void 0,function*(){return new Promise((a,n)=>{e.file(i=>{const l=ke(i,e.fullPath);a(l)},i=>{n(i)})})})}var Te={},Ta;function Dn(){return Ta||(Ta=1,Te.__esModule=!0,Te.default=function(e,a){if(e&&a){var n=Array.isArray(a)?a:a.split(",");if(n.length===0)return!0;var i=e.name||"",l=(e.type||"").toLowerCase(),r=l.replace(/\/.*$/,"");return n.some(function(p){var d=p.trim().toLowerCase();return d.charAt(0)==="."?i.toLowerCase().endsWith(d):d.endsWith("/*")?r===d.replace(/\/.*$/,""):l===d})}return!0}),Te}var Nn=Dn();const ta=Za(Nn);function Aa(e){return Pn(e)||Cn(e)||xt(e)||zn()}function zn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Cn(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Pn(e){if(Array.isArray(e))return ua(e)}function Oa(e,a){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);a&&(i=i.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),n.push.apply(n,i)}return n}function Ra(e){for(var a=1;a<arguments.length;a++){var n=arguments[a]!=null?arguments[a]:{};a%2?Oa(Object(n),!0).forEach(function(i){ft(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Oa(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function ft(e,a,n){return a in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n,e}function Se(e,a){return _n(e)||En(e,a)||xt(e,a)||Sn()}function Sn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xt(e,a){if(e){if(typeof e=="string")return ua(e,a);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ua(e,a)}}function ua(e,a){(a==null||a>e.length)&&(a=e.length);for(var n=0,i=new Array(a);n<a;n++)i[n]=e[n];return i}function En(e,a){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var i=[],l=!0,r=!1,p,d;try{for(n=n.call(e);!(l=(p=n.next()).done)&&(i.push(p.value),!(a&&i.length===a));l=!0);}catch(b){r=!0,d=b}finally{try{!l&&n.return!=null&&n.return()}finally{if(r)throw d}}return i}}function _n(e){if(Array.isArray(e))return e}var Fn=typeof ta=="function"?ta:ta.default,Tn="file-invalid-type",An="file-too-large",On="file-too-small",Rn="too-many-files",Mn=function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=a.split(","),i=n.length>1?"one of ".concat(n.join(", ")):n[0];return{code:Tn,message:"File type must be ".concat(i)}},Ma=function(a){return{code:An,message:"File is larger than ".concat(a," ").concat(a===1?"byte":"bytes")}},Ia=function(a){return{code:On,message:"File is smaller than ".concat(a," ").concat(a===1?"byte":"bytes")}},In={code:Rn,message:"Too many files"};function vt(e,a){var n=e.type==="application/x-moz-file"||Fn(e,a);return[n,n?null:Mn(a)]}function gt(e,a,n){if(ve(e.size))if(ve(a)&&ve(n)){if(e.size>n)return[!1,Ma(n)];if(e.size<a)return[!1,Ia(a)]}else{if(ve(a)&&e.size<a)return[!1,Ia(a)];if(ve(n)&&e.size>n)return[!1,Ma(n)]}return[!0,null]}function ve(e){return e!=null}function qn(e){var a=e.files,n=e.accept,i=e.minSize,l=e.maxSize,r=e.multiple,p=e.maxFiles,d=e.validator;return!r&&a.length>1||r&&p>=1&&a.length>p?!1:a.every(function(b){var f=vt(b,n),x=Se(f,1),_=x[0],y=gt(b,i,l),j=Se(y,1),C=j[0],w=d?d(b):null;return _&&C&&!w})}function Me(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function Ae(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(a){return a==="Files"||a==="application/x-moz-file"}):!!e.target&&!!e.target.files}function qa(e){e.preventDefault()}function Ln(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function Bn(e){return e.indexOf("Edge/")!==-1}function Un(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return Ln(e)||Bn(e)}function ee(){for(var e=arguments.length,a=new Array(e),n=0;n<e;n++)a[n]=arguments[n];return function(i){for(var l=arguments.length,r=new Array(l>1?l-1:0),p=1;p<l;p++)r[p-1]=arguments[p];return a.some(function(d){return!Me(i)&&d&&d.apply(void 0,[i].concat(r)),Me(i)})}}function $n(){return"showOpenFilePicker"in window}function Hn(e){if(ve(e)){var a=Object.entries(e).filter(function(n){var i=Se(n,2),l=i[0],r=i[1],p=!0;return ht(l)||(console.warn('Skipped "'.concat(l,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),p=!1),(!Array.isArray(r)||!r.every(bt))&&(console.warn('Skipped "'.concat(l,'" because an invalid file extension was provided.')),p=!1),p}).reduce(function(n,i){var l=Se(i,2),r=l[0],p=l[1];return Ra(Ra({},n),{},ft({},r,p))},{});return[{description:"Files",accept:a}]}return e}function Kn(e){if(ve(e))return Object.entries(e).reduce(function(a,n){var i=Se(n,2),l=i[0],r=i[1];return[].concat(Aa(a),[l],Aa(r))},[]).filter(function(a){return ht(a)||bt(a)}).join(",")}function Wn(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function Gn(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function ht(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function bt(e){return/^.*\.[\w]+$/.test(e)}var Vn=["children"],Yn=["open"],Jn=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],Qn=["refKey","onChange","onClick"];function Xn(e){return ai(e)||ei(e)||yt(e)||Zn()}function Zn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ei(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ai(e){if(Array.isArray(e))return fa(e)}function na(e,a){return ii(e)||ni(e,a)||yt(e,a)||ti()}function ti(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function yt(e,a){if(e){if(typeof e=="string")return fa(e,a);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return fa(e,a)}}function fa(e,a){(a==null||a>e.length)&&(a=e.length);for(var n=0,i=new Array(a);n<a;n++)i[n]=e[n];return i}function ni(e,a){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var i=[],l=!0,r=!1,p,d;try{for(n=n.call(e);!(l=(p=n.next()).done)&&(i.push(p.value),!(a&&i.length===a));l=!0);}catch(b){r=!0,d=b}finally{try{!l&&n.return!=null&&n.return()}finally{if(r)throw d}}return i}}function ii(e){if(Array.isArray(e))return e}function La(e,a){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);a&&(i=i.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),n.push.apply(n,i)}return n}function M(e){for(var a=1;a<arguments.length;a++){var n=arguments[a]!=null?arguments[a]:{};a%2?La(Object(n),!0).forEach(function(i){xa(e,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):La(Object(n)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(n,i))})}return e}function xa(e,a,n){return a in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n,e}function Ie(e,a){if(e==null)return{};var n=oi(e,a),i,l;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(l=0;l<r.length;l++)i=r[l],!(a.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(e,i)&&(n[i]=e[i])}return n}function oi(e,a){if(e==null)return{};var n={},i=Object.keys(e),l,r;for(r=0;r<i.length;r++)l=i[r],!(a.indexOf(l)>=0)&&(n[l]=e[l]);return n}var He=o.forwardRef(function(e,a){var n=e.children,i=Ie(e,Vn),l=si(i),r=l.open,p=Ie(l,Yn);return o.useImperativeHandle(a,function(){return{open:r}},[r]),Tt.createElement(o.Fragment,null,n(M(M({},p),{},{open:r})))});He.displayName="Dropzone";var jt={disabled:!1,getFilesFromEvent:xn,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};He.defaultProps=jt;He.propTypes={children:F.func,accept:F.objectOf(F.arrayOf(F.string)),multiple:F.bool,preventDropOnDocument:F.bool,noClick:F.bool,noKeyboard:F.bool,noDrag:F.bool,noDragEventsBubbling:F.bool,minSize:F.number,maxSize:F.number,maxFiles:F.number,disabled:F.bool,getFilesFromEvent:F.func,onFileDialogCancel:F.func,onFileDialogOpen:F.func,useFsAccessApi:F.bool,autoFocus:F.bool,onDragEnter:F.func,onDragLeave:F.func,onDragOver:F.func,onDrop:F.func,onDropAccepted:F.func,onDropRejected:F.func,onError:F.func,validator:F.func};var va={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function si(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},a=M(M({},jt),e),n=a.accept,i=a.disabled,l=a.getFilesFromEvent,r=a.maxSize,p=a.minSize,d=a.multiple,b=a.maxFiles,f=a.onDragEnter,x=a.onDragLeave,_=a.onDragOver,y=a.onDrop,j=a.onDropAccepted,C=a.onDropRejected,w=a.onFileDialogCancel,N=a.onFileDialogOpen,g=a.useFsAccessApi,$=a.autoFocus,I=a.preventDropOnDocument,P=a.noClick,u=a.noKeyboard,z=a.noDrag,k=a.noDragEventsBubbling,q=a.onError,D=a.validator,G=o.useMemo(function(){return Kn(n)},[n]),ue=o.useMemo(function(){return Hn(n)},[n]),L=o.useMemo(function(){return typeof N=="function"?N:Ba},[N]),H=o.useMemo(function(){return typeof w=="function"?w:Ba},[w]),R=o.useRef(null),U=o.useRef(null),De=o.useReducer(li,va),ce=na(De,2),te=ce[0],B=ce[1],pe=te.isFocused,K=te.isFileDialogActive,ne=o.useRef(typeof window<"u"&&window.isSecureContext&&g&&$n()),Ne=function(){!ne.current&&K&&setTimeout(function(){if(U.current){var m=U.current.files;m.length||(B({type:"closeDialog"}),H())}},300)};o.useEffect(function(){return window.addEventListener("focus",Ne,!1),function(){window.removeEventListener("focus",Ne,!1)}},[U,K,H,ne]);var V=o.useRef([]),he=function(m){R.current&&R.current.contains(m.target)||(m.preventDefault(),V.current=[])};o.useEffect(function(){return I&&(document.addEventListener("dragover",qa,!1),document.addEventListener("drop",he,!1)),function(){I&&(document.removeEventListener("dragover",qa),document.removeEventListener("drop",he))}},[R,I]),o.useEffect(function(){return!i&&$&&R.current&&R.current.focus(),function(){}},[R,$,i]);var ie=o.useCallback(function(s){q?q(s):console.error(s)},[q]),de=o.useCallback(function(s){s.preventDefault(),s.persist(),Z(s),V.current=[].concat(Xn(V.current),[s.target]),Ae(s)&&Promise.resolve(l(s)).then(function(m){if(!(Me(s)&&!k)){var v=m.length,h=v>0&&qn({files:m,accept:G,minSize:p,maxSize:r,multiple:d,maxFiles:b,validator:D}),S=v>0&&!h;B({isDragAccept:h,isDragReject:S,isDragActive:!0,type:"setDraggedFiles"}),f&&f(s)}}).catch(function(m){return ie(m)})},[l,f,ie,k,G,p,r,d,b,D]),ze=o.useCallback(function(s){s.preventDefault(),s.persist(),Z(s);var m=Ae(s);if(m&&s.dataTransfer)try{s.dataTransfer.dropEffect="copy"}catch{}return m&&_&&_(s),!1},[_,k]),Ee=o.useCallback(function(s){s.preventDefault(),s.persist(),Z(s);var m=V.current.filter(function(h){return R.current&&R.current.contains(h)}),v=m.indexOf(s.target);v!==-1&&m.splice(v,1),V.current=m,!(m.length>0)&&(B({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Ae(s)&&x&&x(s))},[R,x,k]),oe=o.useCallback(function(s,m){var v=[],h=[];s.forEach(function(S){var A=vt(S,G),se=na(A,2),ye=se[0],je=se[1],we=gt(S,p,r),Fe=na(we,2),Ge=Fe[0],Ve=Fe[1],Ye=D?D(S):null;if(ye&&Ge&&!Ye)v.push(S);else{var Je=[je,Ve];Ye&&(Je=Je.concat(Ye)),h.push({file:S,errors:Je.filter(function(kt){return kt})})}}),(!d&&v.length>1||d&&b>=1&&v.length>b)&&(v.forEach(function(S){h.push({file:S,errors:[In]})}),v.splice(0)),B({acceptedFiles:v,fileRejections:h,isDragReject:h.length>0,type:"setFiles"}),y&&y(v,h,m),h.length>0&&C&&C(h,m),v.length>0&&j&&j(v,m)},[B,d,G,p,r,b,y,j,C,D]),Y=o.useCallback(function(s){s.preventDefault(),s.persist(),Z(s),V.current=[],Ae(s)&&Promise.resolve(l(s)).then(function(m){Me(s)&&!k||oe(m,s)}).catch(function(m){return ie(m)}),B({type:"reset"})},[l,oe,ie,k]),J=o.useCallback(function(){if(ne.current){B({type:"openDialog"}),L();var s={multiple:d,types:ue};window.showOpenFilePicker(s).then(function(m){return l(m)}).then(function(m){oe(m,null),B({type:"closeDialog"})}).catch(function(m){Wn(m)?(H(m),B({type:"closeDialog"})):Gn(m)?(ne.current=!1,U.current?(U.current.value=null,U.current.click()):ie(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):ie(m)});return}U.current&&(B({type:"openDialog"}),L(),U.current.value=null,U.current.click())},[B,L,H,g,oe,ie,ue,d]),fe=o.useCallback(function(s){!R.current||!R.current.isEqualNode(s.target)||(s.key===" "||s.key==="Enter"||s.keyCode===32||s.keyCode===13)&&(s.preventDefault(),J())},[R,J]),Q=o.useCallback(function(){B({type:"focus"})},[]),W=o.useCallback(function(){B({type:"blur"})},[]),_e=o.useCallback(function(){P||(Un()?setTimeout(J,0):J())},[P,J]),X=function(m){return i?null:m},xe=function(m){return u?null:X(m)},be=function(m){return z?null:X(m)},Z=function(m){k&&m.stopPropagation()},Ke=o.useMemo(function(){return function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},m=s.refKey,v=m===void 0?"ref":m,h=s.role,S=s.onKeyDown,A=s.onFocus,se=s.onBlur,ye=s.onClick,je=s.onDragEnter,we=s.onDragOver,Fe=s.onDragLeave,Ge=s.onDrop,Ve=Ie(s,Jn);return M(M(xa({onKeyDown:xe(ee(S,fe)),onFocus:xe(ee(A,Q)),onBlur:xe(ee(se,W)),onClick:X(ee(ye,_e)),onDragEnter:be(ee(je,de)),onDragOver:be(ee(we,ze)),onDragLeave:be(ee(Fe,Ee)),onDrop:be(ee(Ge,Y)),role:typeof h=="string"&&h!==""?h:"presentation"},v,R),!i&&!u?{tabIndex:0}:{}),Ve)}},[R,fe,Q,W,_e,de,ze,Ee,Y,u,z,i]),We=o.useCallback(function(s){s.stopPropagation()},[]),c=o.useMemo(function(){return function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},m=s.refKey,v=m===void 0?"ref":m,h=s.onChange,S=s.onClick,A=Ie(s,Qn),se=xa({accept:G,multiple:d,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:X(ee(h,Y)),onClick:X(ee(S,We)),tabIndex:-1},v,U);return M(M({},se),A)}},[U,n,d,Y,i]);return M(M({},te),{},{isFocused:pe&&!i,getRootProps:Ke,getInputProps:c,rootRef:R,inputRef:U,open:X(J)})}function li(e,a){switch(a.type){case"focus":return M(M({},e),{},{isFocused:!0});case"blur":return M(M({},e),{},{isFocused:!1});case"openDialog":return M(M({},va),{},{isFileDialogActive:!0});case"closeDialog":return M(M({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return M(M({},e),{},{isDragActive:a.isDragActive,isDragAccept:a.isDragAccept,isDragReject:a.isDragReject});case"setFiles":return M(M({},e),{},{acceptedFiles:a.acceptedFiles,fileRejections:a.fileRejections,isDragReject:a.isDragReject});case"reset":return M({},va);default:return e}}function Ba(){}function ga(e,a={}){const{decimals:n=0,sizeType:i="normal"}=a,l=["Bytes","KB","MB","GB","TB"],r=["Bytes","KiB","MiB","GiB","TiB"];if(e===0)return"0 Byte";const p=Math.floor(Math.log(e)/Math.log(1024));return`${(e/Math.pow(1024,p)).toFixed(n)} ${i==="accurate"?r[p]??"Bytes":l[p]??"Bytes"}`}function ri(e){const{t:a}=me(),{value:n,onValueChange:i,onUpload:l,onReject:r,progresses:p,fileErrors:d,accept:b=Mt,maxSize:f=1024*1024*200,maxFileCount:x=1,multiple:_=!1,disabled:y=!1,description:j,className:C,...w}=e,[N,g]=Ft({prop:n,onChange:i}),$=o.useCallback((u,z)=>{const k=((N==null?void 0:N.length)??0)+u.length+z.length;if(!_&&x===1&&u.length+z.length>1){O.error(a("documentPanel.uploadDocuments.fileUploader.singleFileLimit"));return}if(k>x){O.error(a("documentPanel.uploadDocuments.fileUploader.maxFilesLimit",{count:x}));return}z.length>0&&(r?r(z):z.forEach(({file:L})=>{O.error(a("documentPanel.uploadDocuments.fileUploader.fileRejected",{name:L.name}))}));const q=u.map(L=>Object.assign(L,{preview:URL.createObjectURL(L)})),D=z.map(({file:L})=>Object.assign(L,{preview:URL.createObjectURL(L),rejected:!0})),G=[...q,...D],ue=N?[...N,...G]:G;if(g(ue),l&&u.length>0){const L=u.filter(H=>{var ce;if(!H.name)return!1;const R=`.${((ce=H.name.split(".").pop())==null?void 0:ce.toLowerCase())||""}`,U=Object.entries(b||{}).some(([te,B])=>H.type===te||Array.isArray(B)&&B.includes(R)),De=H.size<=f;return U&&De});L.length>0&&l(L)}},[N,x,_,l,r,g,a,b,f]);function I(u){if(!N)return;const z=N.filter((k,q)=>q!==u);g(z),i==null||i(z)}o.useEffect(()=>()=>{N&&N.forEach(u=>{wt(u)&&URL.revokeObjectURL(u.preview)})},[]);const P=y||((N==null?void 0:N.length)??0)>=x;return t.jsxs("div",{className:"relative flex flex-col gap-6 overflow-hidden",children:[t.jsx(He,{onDrop:$,noClick:!1,noKeyboard:!1,maxSize:f,maxFiles:x,multiple:x>1||_,disabled:P,validator:u=>{var q;if(!u.name)return{code:"invalid-file-name",message:a("documentPanel.uploadDocuments.fileUploader.invalidFileName",{fallback:"Invalid file name"})};const z=`.${((q=u.name.split(".").pop())==null?void 0:q.toLowerCase())||""}`;return Object.entries(b||{}).some(([D,G])=>u.type===D||Array.isArray(G)&&G.includes(z))?u.size>f?{code:"file-too-large",message:a("documentPanel.uploadDocuments.fileUploader.fileTooLarge",{maxSize:ga(f)})}:null:{code:"file-invalid-type",message:a("documentPanel.uploadDocuments.fileUploader.unsupportedType")}},children:({getRootProps:u,getInputProps:z,isDragActive:k})=>t.jsxs("div",{...u(),className:E("group border-muted-foreground/25 hover:bg-muted/25 relative grid h-52 w-full cursor-pointer place-items-center rounded-lg border-2 border-dashed px-5 py-2.5 text-center transition","ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none",k&&"border-muted-foreground/50",P&&"pointer-events-none opacity-60",C),...w,children:[t.jsx("input",{...z()}),k?t.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[t.jsx("div",{className:"rounded-full border border-dashed p-3",children:t.jsx(la,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),t.jsx("p",{className:"text-muted-foreground font-medium",children:a("documentPanel.uploadDocuments.fileUploader.dropHere")})]}):t.jsxs("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[t.jsx("div",{className:"rounded-full border border-dashed p-3",children:t.jsx(la,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),t.jsxs("div",{className:"flex flex-col gap-px",children:[t.jsx("p",{className:"text-muted-foreground font-medium",children:a("documentPanel.uploadDocuments.fileUploader.dragAndDrop")}),j?t.jsx("p",{className:"text-muted-foreground/70 text-sm",children:j}):t.jsxs("p",{className:"text-muted-foreground/70 text-sm",children:[a("documentPanel.uploadDocuments.fileUploader.uploadDescription",{count:x,isMultiple:x===1/0,maxSize:ga(f)}),a("documentPanel.uploadDocuments.fileTypes")]})]})]})]})}),N!=null&&N.length?t.jsx(It,{className:"h-fit w-full px-3",children:t.jsx("div",{className:"flex max-h-48 flex-col gap-4",children:N==null?void 0:N.map((u,z)=>t.jsx(ci,{file:u,onRemove:()=>I(z),progress:p==null?void 0:p[u.name],error:d==null?void 0:d[u.name]},z))})}):null]})}function Ua({value:e,error:a}){return t.jsx("div",{className:"relative h-2 w-full",children:t.jsx("div",{className:"h-full w-full overflow-hidden rounded-full bg-secondary",children:t.jsx("div",{className:E("h-full transition-all",a?"bg-red-400":"bg-primary"),style:{width:`${e}%`}})})})}function ci({file:e,progress:a,error:n,onRemove:i}){const{t:l}=me();return t.jsxs("div",{className:"relative flex items-center gap-2.5",children:[t.jsxs("div",{className:"flex flex-1 gap-2.5",children:[n?t.jsx(tt,{className:"text-red-400 size-10","aria-hidden":"true"}):wt(e)?t.jsx(pi,{file:e}):null,t.jsxs("div",{className:"flex w-full flex-col gap-2",children:[t.jsxs("div",{className:"flex flex-col gap-px",children:[t.jsx("p",{className:"text-foreground/80 line-clamp-1 text-sm font-medium",children:e.name}),t.jsx("p",{className:"text-muted-foreground text-xs",children:ga(e.size)})]}),n?t.jsxs("div",{className:"text-red-400 text-sm",children:[t.jsx("div",{className:"relative mb-2",children:t.jsx(Ua,{value:100,error:!0})}),t.jsx("p",{children:n})]}):a?t.jsx(Ua,{value:a}):null]})]}),t.jsx("div",{className:"flex items-center gap-2",children:t.jsxs(T,{type:"button",variant:"outline",size:"icon",className:"size-7",onClick:i,children:[t.jsx(nt,{className:"size-4","aria-hidden":"true"}),t.jsx("span",{className:"sr-only",children:l("documentPanel.uploadDocuments.fileUploader.removeFile")})]})})]})}function wt(e){return"preview"in e&&typeof e.preview=="string"}function pi({file:e}){return e.type.startsWith("image/")?t.jsx("div",{className:"aspect-square shrink-0 rounded-md object-cover"}):t.jsx(tt,{className:"text-muted-foreground size-10","aria-hidden":"true"})}function di({onDocumentsUploaded:e}){const{t:a}=me(),[n,i]=o.useState(!1),[l,r]=o.useState(!1),[p,d]=o.useState({}),[b,f]=o.useState({}),x=o.useCallback(y=>{y.forEach(({file:j,errors:C})=>{var N;let w=((N=C[0])==null?void 0:N.message)||a("documentPanel.uploadDocuments.fileUploader.fileRejected",{name:j.name});w.includes("file-invalid-type")&&(w=a("documentPanel.uploadDocuments.fileUploader.unsupportedType")),d(g=>({...g,[j.name]:100})),f(g=>({...g,[j.name]:w}))})},[d,f,a]),_=o.useCallback(async y=>{var w,N;r(!0);let j=!1;f(g=>{const $={...g};return y.forEach(I=>{delete $[I.name]}),$});const C=O.loading(a("documentPanel.uploadDocuments.batch.uploading"));try{const g={},$=new Intl.Collator(["zh-CN","en"],{sensitivity:"accent",numeric:!0}),I=[...y].sort((u,z)=>$.compare(u.name,z.name));for(const u of I)try{d(k=>({...k,[u.name]:0}));const z=await qt(u,k=>{console.debug(a("documentPanel.uploadDocuments.single.uploading",{name:u.name,percent:k})),d(q=>({...q,[u.name]:k}))});z.status==="duplicated"?(g[u.name]=a("documentPanel.uploadDocuments.fileUploader.duplicateFile"),f(k=>({...k,[u.name]:a("documentPanel.uploadDocuments.fileUploader.duplicateFile")}))):z.status!=="success"?(g[u.name]=z.message,f(k=>({...k,[u.name]:z.message}))):j=!0}catch(z){console.error(`Upload failed for ${u.name}:`,z);let k=ae(z);if(z&&typeof z=="object"&&"response"in z){const q=z;((w=q.response)==null?void 0:w.status)===400&&(k=((N=q.response.data)==null?void 0:N.detail)||k),d(D=>({...D,[u.name]:100}))}g[u.name]=k,f(q=>({...q,[u.name]:k}))}Object.keys(g).length>0?O.error(a("documentPanel.uploadDocuments.batch.error"),{id:C}):O.success(a("documentPanel.uploadDocuments.batch.success"),{id:C}),j&&e&&e().catch(u=>{console.error("Error refreshing documents:",u)})}catch(g){console.error("Unexpected error during upload:",g),O.error(a("documentPanel.uploadDocuments.generalError",{error:ae(g)}),{id:C})}finally{r(!1)}},[r,d,f,a,e]);return t.jsxs(qe,{open:n,onOpenChange:y=>{l||(y||(d({}),f({})),i(y))},children:[t.jsx(ha,{asChild:!0,children:t.jsxs(T,{variant:"default",side:"bottom",tooltip:a("documentPanel.uploadDocuments.tooltip"),size:"sm",children:[t.jsx(la,{})," ",a("documentPanel.uploadDocuments.button")]})}),t.jsxs(Le,{className:"sm:max-w-xl",onCloseAutoFocus:y=>y.preventDefault(),children:[t.jsxs(Be,{children:[t.jsx(Ue,{children:a("documentPanel.uploadDocuments.title")}),t.jsx($e,{children:a("documentPanel.uploadDocuments.description")})]}),t.jsx(ri,{maxFileCount:1/0,maxSize:200*1024*1024,description:a("documentPanel.uploadDocuments.fileTypes"),onUpload:_,onReject:x,progresses:p,fileErrors:b,disabled:l})]})]})}const $a=({htmlFor:e,className:a,children:n,...i})=>t.jsx("label",{htmlFor:e,className:a,...i,children:n});function mi({onDocumentsCleared:e}){const{t:a}=me(),[n,i]=o.useState(!1),[l,r]=o.useState(""),[p,d]=o.useState(!1),[b,f]=o.useState(!1),x=o.useRef(null),_=l.toLowerCase()==="yes",y=3e4;o.useEffect(()=>{n||(r(""),d(!1),f(!1),x.current&&(clearTimeout(x.current),x.current=null))},[n]),o.useEffect(()=>()=>{x.current&&clearTimeout(x.current)},[]);const j=o.useCallback(async()=>{if(!(!_||b)){f(!0),x.current=setTimeout(()=>{b&&(O.error(a("documentPanel.clearDocuments.timeout")),f(!1),r(""))},y);try{const C=await Lt();if(C.status!=="success"){O.error(a("documentPanel.clearDocuments.failed",{message:C.message})),r("");return}if(O.success(a("documentPanel.clearDocuments.success")),p)try{await Bt(),O.success(a("documentPanel.clearDocuments.cacheCleared"))}catch(w){O.error(a("documentPanel.clearDocuments.cacheClearFailed",{error:ae(w)}))}e&&e().catch(console.error),i(!1)}catch(C){O.error(a("documentPanel.clearDocuments.error",{error:ae(C)})),r("")}finally{x.current&&(clearTimeout(x.current),x.current=null),f(!1)}}},[_,b,p,i,a,e,y]);return t.jsxs(qe,{open:n,onOpenChange:i,children:[t.jsx(ha,{asChild:!0,children:t.jsxs(T,{variant:"outline",side:"bottom",tooltip:a("documentPanel.clearDocuments.tooltip"),size:"sm",children:[t.jsx(Ut,{})," ",a("documentPanel.clearDocuments.button")]})}),t.jsxs(Le,{className:"sm:max-w-xl",onCloseAutoFocus:C=>C.preventDefault(),children:[t.jsxs(Be,{children:[t.jsxs(Ue,{className:"flex items-center gap-2 text-red-500 dark:text-red-400 font-bold",children:[t.jsx(it,{className:"h-5 w-5"}),a("documentPanel.clearDocuments.title")]}),t.jsx($e,{className:"pt-2",children:a("documentPanel.clearDocuments.description")})]}),t.jsx("div",{className:"text-red-500 dark:text-red-400 font-semibold mb-4",children:a("documentPanel.clearDocuments.warning")}),t.jsx("div",{className:"mb-4",children:a("documentPanel.clearDocuments.confirm")}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{className:"space-y-2",children:[t.jsx($a,{htmlFor:"confirm-text",className:"text-sm font-medium",children:a("documentPanel.clearDocuments.confirmPrompt")}),t.jsx(Oe,{id:"confirm-text",value:l,onChange:C=>r(C.target.value),placeholder:a("documentPanel.clearDocuments.confirmPlaceholder"),className:"w-full",disabled:b})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(ot,{id:"clear-cache",checked:p,onCheckedChange:C=>d(C===!0),disabled:b}),t.jsx($a,{htmlFor:"clear-cache",className:"text-sm font-medium cursor-pointer",children:a("documentPanel.clearDocuments.clearCache")})]})]}),t.jsxs(st,{children:[t.jsx(T,{variant:"outline",onClick:()=>i(!1),disabled:b,children:a("common.cancel")}),t.jsx(T,{variant:"destructive",onClick:j,disabled:!_||b,children:b?t.jsxs(t.Fragment,{children:[t.jsx($t,{className:"mr-2 h-4 w-4 animate-spin"}),a("documentPanel.clearDocuments.clearing")]}):a("documentPanel.clearDocuments.confirmButton")})]})]})]})}const Ha=({htmlFor:e,className:a,children:n,...i})=>t.jsx("label",{htmlFor:e,className:a,...i,children:n});function ui({selectedDocIds:e,onDocumentsDeleted:a}){const{t:n}=me(),[i,l]=o.useState(!1),[r,p]=o.useState(""),[d,b]=o.useState(!1),[f,x]=o.useState(!1),_=r.toLowerCase()==="yes"&&!f;o.useEffect(()=>{i||(p(""),b(!1),x(!1))},[i]);const y=o.useCallback(async()=>{if(!(!_||e.length===0)){x(!0);try{const j=await Ht(e,d);if(j.status==="deletion_started")O.success(n("documentPanel.deleteDocuments.success",{count:e.length}));else if(j.status==="busy"){O.error(n("documentPanel.deleteDocuments.busy")),p(""),x(!1);return}else if(j.status==="not_allowed"){O.error(n("documentPanel.deleteDocuments.notAllowed")),p(""),x(!1);return}else{O.error(n("documentPanel.deleteDocuments.failed",{message:j.message})),p(""),x(!1);return}a&&a().catch(console.error),l(!1)}catch(j){O.error(n("documentPanel.deleteDocuments.error",{error:ae(j)})),p("")}finally{x(!1)}}},[_,e,d,l,n,a]);return t.jsxs(qe,{open:i,onOpenChange:l,children:[t.jsx(ha,{asChild:!0,children:t.jsxs(T,{variant:"destructive",side:"bottom",tooltip:n("documentPanel.deleteDocuments.tooltip",{count:e.length}),size:"sm",children:[t.jsx(Kt,{})," ",n("documentPanel.deleteDocuments.button")]})}),t.jsxs(Le,{className:"sm:max-w-xl",onCloseAutoFocus:j=>j.preventDefault(),children:[t.jsxs(Be,{children:[t.jsxs(Ue,{className:"flex items-center gap-2 text-red-500 dark:text-red-400 font-bold",children:[t.jsx(it,{className:"h-5 w-5"}),n("documentPanel.deleteDocuments.title")]}),t.jsx($e,{className:"pt-2",children:n("documentPanel.deleteDocuments.description",{count:e.length})})]}),t.jsx("div",{className:"text-red-500 dark:text-red-400 font-semibold mb-4",children:n("documentPanel.deleteDocuments.warning")}),t.jsx("div",{className:"mb-4",children:n("documentPanel.deleteDocuments.confirm",{count:e.length})}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{className:"space-y-2",children:[t.jsx(Ha,{htmlFor:"confirm-text",className:"text-sm font-medium",children:n("documentPanel.deleteDocuments.confirmPrompt")}),t.jsx(Oe,{id:"confirm-text",value:r,onChange:j=>p(j.target.value),placeholder:n("documentPanel.deleteDocuments.confirmPlaceholder"),className:"w-full",disabled:f})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"checkbox",id:"delete-file",checked:d,onChange:j=>b(j.target.checked),disabled:f,className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"}),t.jsx(Ha,{htmlFor:"delete-file",className:"text-sm font-medium cursor-pointer",children:n("documentPanel.deleteDocuments.deleteFileOption")})]})]}),t.jsxs(st,{children:[t.jsx(T,{variant:"outline",onClick:()=>l(!1),disabled:f,children:n("common.cancel")}),t.jsx(T,{variant:"destructive",onClick:y,disabled:!_,children:n(f?"documentPanel.deleteDocuments.deleting":"documentPanel.deleteDocuments.confirmButton")})]})]})]})}const Ka=[{value:10,label:"10"},{value:20,label:"20"},{value:50,label:"50"},{value:100,label:"100"},{value:200,label:"200"}];function fi({currentPage:e,totalPages:a,pageSize:n,totalCount:i,onPageChange:l,onPageSizeChange:r,isLoading:p=!1,compact:d=!1,className:b}){const{t:f}=me(),[x,_]=o.useState(e.toString());o.useEffect(()=>{_(e.toString())},[e]);const y=o.useCallback(P=>{_(P)},[]),j=o.useCallback(()=>{const P=parseInt(x,10);!isNaN(P)&&P>=1&&P<=a?l(P):_(e.toString())},[x,a,l,e]),C=o.useCallback(P=>{P.key==="Enter"&&j()},[j]),w=o.useCallback(P=>{const u=parseInt(P,10);isNaN(u)||r(u)},[r]),N=o.useCallback(()=>{e>1&&!p&&l(1)},[e,l,p]),g=o.useCallback(()=>{e>1&&!p&&l(e-1)},[e,l,p]),$=o.useCallback(()=>{e<a&&!p&&l(e+1)},[e,a,l,p]),I=o.useCallback(()=>{e<a&&!p&&l(a)},[e,a,l,p]);return a<=1?null:d?t.jsxs("div",{className:E("flex items-center gap-2",b),children:[t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(T,{variant:"outline",size:"sm",onClick:g,disabled:e<=1||p,className:"h-8 w-8 p-0",children:t.jsx(ba,{className:"h-4 w-4"})}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(Oe,{type:"text",value:x,onChange:P=>y(P.target.value),onBlur:j,onKeyPress:C,disabled:p,className:"h-8 w-12 text-center text-sm"}),t.jsxs("span",{className:"text-sm text-gray-500",children:["/ ",a]})]}),t.jsx(T,{variant:"outline",size:"sm",onClick:$,disabled:e>=a||p,className:"h-8 w-8 p-0",children:t.jsx(ya,{className:"h-4 w-4"})})]}),t.jsxs(Na,{value:n.toString(),onValueChange:w,disabled:p,children:[t.jsx(ra,{className:"h-8 w-16",children:t.jsx(za,{})}),t.jsx(ca,{children:Ka.map(P=>t.jsx(pa,{value:P.value.toString(),children:P.label},P.value))})]})]}):t.jsxs("div",{className:E("flex items-center justify-between gap-4",b),children:[t.jsx("div",{className:"text-sm text-gray-500",children:f("pagination.showing",{start:Math.min((e-1)*n+1,i),end:Math.min(e*n,i),total:i})}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(T,{variant:"outline",size:"sm",onClick:N,disabled:e<=1||p,className:"h-8 w-8 p-0",tooltip:f("pagination.firstPage"),children:t.jsx(Wt,{className:"h-4 w-4"})}),t.jsx(T,{variant:"outline",size:"sm",onClick:g,disabled:e<=1||p,className:"h-8 w-8 p-0",tooltip:f("pagination.prevPage"),children:t.jsx(ba,{className:"h-4 w-4"})}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx("span",{className:"text-sm",children:f("pagination.page")}),t.jsx(Oe,{type:"text",value:x,onChange:P=>y(P.target.value),onBlur:j,onKeyPress:C,disabled:p,className:"h-8 w-16 text-center text-sm"}),t.jsxs("span",{className:"text-sm",children:["/ ",a]})]}),t.jsx(T,{variant:"outline",size:"sm",onClick:$,disabled:e>=a||p,className:"h-8 w-8 p-0",tooltip:f("pagination.nextPage"),children:t.jsx(ya,{className:"h-4 w-4"})}),t.jsx(T,{variant:"outline",size:"sm",onClick:I,disabled:e>=a||p,className:"h-8 w-8 p-0",tooltip:f("pagination.lastPage"),children:t.jsx(Gt,{className:"h-4 w-4"})})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("span",{className:"text-sm",children:f("pagination.pageSize")}),t.jsxs(Na,{value:n.toString(),onValueChange:w,disabled:p,children:[t.jsx(ra,{className:"h-8 w-16",children:t.jsx(za,{})}),t.jsx(ca,{children:Ka.map(P=>t.jsx(pa,{value:P.value.toString(),children:P.label},P.value))})]})]})]})]})}function xi({open:e,onOpenChange:a}){var _;const{t:n}=me(),[i,l]=o.useState(null),[r,p]=o.useState("center"),[d,b]=o.useState(!1),f=o.useRef(null);o.useEffect(()=>{e&&(p("center"),b(!1))},[e]),o.useEffect(()=>{const y=f.current;!y||d||(y.scrollTop=y.scrollHeight)},[i==null?void 0:i.history_messages,d]);const x=()=>{const y=f.current;if(!y)return;const j=Math.abs(y.scrollHeight-y.scrollTop-y.clientHeight)<1;b(!j)};return o.useEffect(()=>{if(!e)return;const y=async()=>{try{const C=await Qt();l(C)}catch(C){O.error(n("documentPanel.pipelineStatus.errors.fetchFailed",{error:ae(C)}))}};y();const j=setInterval(y,2e3);return()=>clearInterval(j)},[e,n]),t.jsx(qe,{open:e,onOpenChange:a,children:t.jsxs(Le,{className:E("sm:max-w-[800px] transition-all duration-200 fixed",r==="left"&&"!left-[25%] !translate-x-[-50%] !mx-4",r==="center"&&"!left-1/2 !-translate-x-1/2",r==="right"&&"!left-[75%] !translate-x-[-50%] !mx-4"),children:[t.jsx($e,{className:"sr-only",children:i!=null&&i.job_name?`${n("documentPanel.pipelineStatus.jobName")}: ${i.job_name}, ${n("documentPanel.pipelineStatus.progress")}: ${i.cur_batch}/${i.batchs}`:n("documentPanel.pipelineStatus.noActiveJob")}),t.jsxs(Be,{className:"flex flex-row items-center",children:[t.jsx(Ue,{className:"flex-1",children:n("documentPanel.pipelineStatus.title")}),t.jsxs("div",{className:"flex items-center gap-2 mr-8",children:[t.jsx(T,{variant:"ghost",size:"icon",className:E("h-6 w-6",r==="left"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>p("left"),children:t.jsx(Vt,{className:"h-4 w-4"})}),t.jsx(T,{variant:"ghost",size:"icon",className:E("h-6 w-6",r==="center"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>p("center"),children:t.jsx(Yt,{className:"h-4 w-4"})}),t.jsx(T,{variant:"ghost",size:"icon",className:E("h-6 w-6",r==="right"&&"bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-700 dark:text-zinc-200 dark:hover:bg-zinc-600"),onClick:()=>p("right"),children:t.jsx(Jt,{className:"h-4 w-4"})})]})]}),t.jsxs("div",{className:"space-y-4 pt-4",children:[t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.busy"),":"]}),t.jsx("div",{className:`h-2 w-2 rounded-full ${i!=null&&i.busy?"bg-green-500":"bg-gray-300"}`})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.requestPending"),":"]}),t.jsx("div",{className:`h-2 w-2 rounded-full ${i!=null&&i.request_pending?"bg-green-500":"bg-gray-300"}`})]})]}),t.jsxs("div",{className:"rounded-md border p-3 space-y-2",children:[t.jsxs("div",{children:[n("documentPanel.pipelineStatus.jobName"),": ",(i==null?void 0:i.job_name)||"-"]}),t.jsxs("div",{className:"flex justify-between",children:[t.jsxs("span",{children:[n("documentPanel.pipelineStatus.startTime"),": ",i!=null&&i.job_start?new Date(i.job_start).toLocaleString(void 0,{year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}):"-"]}),t.jsxs("span",{children:[n("documentPanel.pipelineStatus.progress"),": ",i?`${i.cur_batch}/${i.batchs} ${n("documentPanel.pipelineStatus.unit")}`:"-"]})]})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.latestMessage"),":"]}),t.jsx("div",{className:"font-mono text-xs rounded-md bg-zinc-800 text-zinc-100 p-3 whitespace-pre-wrap break-words",children:(i==null?void 0:i.latest_message)||"-"})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"text-sm font-medium",children:[n("documentPanel.pipelineStatus.historyMessages"),":"]}),t.jsx("div",{ref:f,onScroll:x,className:"font-mono text-xs rounded-md bg-zinc-800 text-zinc-100 p-3 overflow-y-auto min-h-[7.5em] max-h-[40vh]",children:(_=i==null?void 0:i.history_messages)!=null&&_.length?i.history_messages.map((y,j)=>t.jsx("div",{className:"whitespace-pre-wrap break-words",children:y},j)):"-"})]})]})]})})}const ia=(e,a=20)=>{if(!e.file_path||typeof e.file_path!="string"||e.file_path.trim()==="")return e.id;const n=e.file_path.split("/"),i=n[n.length-1];return!i||i.trim()===""?e.id:i.length>a?i.slice(0,a)+"...":i},vi=`
/* Tooltip styles */
.tooltip-container {
  position: relative;
  overflow: visible !important;
}

.tooltip {
  position: fixed; /* Use fixed positioning to escape overflow constraints */
  z-index: 9999; /* Ensure tooltip appears above all other elements */
  max-width: 600px;
  white-space: normal;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(0, 0, 0, 0.95);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  pointer-events: none; /* Prevent tooltip from interfering with mouse events */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.15s, visibility 0.15s;
}

.tooltip.visible {
  opacity: 1;
  visibility: visible;
}

.dark .tooltip {
  background-color: rgba(255, 255, 255, 0.95);
  color: black;
}

/* Position tooltip helper class */
.tooltip-helper {
  position: absolute;
  visibility: hidden;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
}

@keyframes pulse {
  0% {
    background-color: rgb(255 0 0 / 0.1);
    border-color: rgb(255 0 0 / 0.2);
  }
  50% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
  100% {
    background-color: rgb(255 0 0 / 0.1);
    border-color: rgb(255 0 0 / 0.2);
  }
}

.dark .pipeline-busy {
  animation: dark-pulse 2s infinite;
}

@keyframes dark-pulse {
  0% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
  50% {
    background-color: rgb(255 0 0 / 0.3);
    border-color: rgb(255 0 0 / 0.6);
  }
  100% {
    background-color: rgb(255 0 0 / 0.2);
    border-color: rgb(255 0 0 / 0.4);
  }
}

.pipeline-busy {
  animation: pulse 2s infinite;
  border: 1px solid;
}
`;function ji(){const e=o.useRef(!0);o.useEffect(()=>{e.current=!0;const c=()=>{e.current=!1};return window.addEventListener("beforeunload",c),()=>{e.current=!1,window.removeEventListener("beforeunload",c)}},[]);const[a,n]=o.useState(!1),{t:i,i18n:l}=me(),r=Ce.use.health(),p=Ce.use.pipelineBusy(),[d,b]=o.useState(null),f=Pe.use.currentTab(),x=Pe.use.showFileName(),_=Pe.use.setShowFileName(),y=Pe.use.documentsPageSize(),j=Pe.use.setDocumentsPageSize(),[,C]=o.useState([]),[w,N]=o.useState({page:1,page_size:y,total_count:0,total_pages:0,has_next:!1,has_prev:!1}),[g,$]=o.useState({all:0}),[I,P]=o.useState(!1),[u,z]=o.useState("updated_at"),[k,q]=o.useState("desc"),[D,G]=o.useState("all"),[ue,L]=o.useState({all:1,processed:1,processing:1,pending:1,failed:1}),[H,R]=o.useState([]),U=H.length>0,De=o.useCallback((c,s)=>{R(m=>s?[...m,c]:m.filter(v=>v!==c))},[]),ce=o.useCallback(()=>{R([])},[]),te=c=>{let s=c;c==="id"&&(s=x?"file_path":"id");const m=u===s&&k==="desc"?"asc":"desc";z(s),q(m),N(v=>({...v,page:1})),L({all:1,processed:1,processing:1,pending:1,failed:1})},B=o.useCallback(c=>[...c].sort((s,m)=>{let v,h;u==="id"&&x?(v=ia(s),h=ia(m)):u==="id"?(v=s.id,h=m.id):(v=new Date(s[u]).getTime(),h=new Date(m[u]).getTime());const S=k==="asc"?1:-1;return typeof v=="string"&&typeof h=="string"?S*v.localeCompare(h):S*(v>h?1:v<h?-1:0)}),[u,k,x]),pe=o.useMemo(()=>{if(!d)return null;const c=[];return D==="all"?Object.entries(d.statuses).forEach(([s,m])=>{m.forEach(v=>{c.push({...v,status:s})})}):(d.statuses[D]||[]).forEach(m=>{c.push({...m,status:D})}),u&&k?B(c):c},[d,u,k,D,B]),K=o.useMemo(()=>(pe==null?void 0:pe.map(c=>c.id))||[],[pe]),ne=o.useMemo(()=>K.filter(c=>H.includes(c)).length,[K,H]),Ne=o.useMemo(()=>K.length>0&&ne===K.length,[K,ne]),V=o.useMemo(()=>ne>0,[ne]),he=o.useCallback(()=>{R(K)},[K]),ie=o.useCallback(()=>V?Ne?{text:i("documentPanel.selectDocuments.deselectAll",{count:K.length}),action:ce,icon:nt}:{text:i("documentPanel.selectDocuments.selectCurrentPage",{count:K.length}),action:he,icon:ja}:{text:i("documentPanel.selectDocuments.selectCurrentPage",{count:K.length}),action:he,icon:ja},[V,Ne,K.length,he,ce,i]),de=o.useMemo(()=>{if(!d)return{all:0};const c={all:0};return Object.entries(d.statuses).forEach(([s,m])=>{c[s]=m.length,c.all+=m.length}),c},[d]),ze=o.useRef({processed:0,processing:0,pending:0,failed:0});o.useEffect(()=>{const c=document.createElement("style");return c.textContent=vi,document.head.appendChild(c),()=>{document.head.removeChild(c)}},[]);const Ee=o.useRef(null);o.useEffect(()=>{if(!d)return;const c=()=>{document.querySelectorAll(".tooltip-container").forEach(h=>{const S=h.querySelector(".tooltip");if(!S||!S.classList.contains("visible"))return;const A=h.getBoundingClientRect();S.style.left=`${A.left}px`,S.style.top=`${A.top-5}px`,S.style.transform="translateY(-100%)"})},s=v=>{const S=v.target.closest(".tooltip-container");if(!S)return;const A=S.querySelector(".tooltip");A&&(A.classList.add("visible"),c())},m=v=>{const S=v.target.closest(".tooltip-container");if(!S)return;const A=S.querySelector(".tooltip");A&&A.classList.remove("visible")};return document.addEventListener("mouseover",s),document.addEventListener("mouseout",m),()=>{document.removeEventListener("mouseover",s),document.removeEventListener("mouseout",m)}},[d]);const oe=o.useCallback(async(c,s,m)=>{try{if(!e.current)return;P(!0);const h=await wa({status_filter:m==="all"?null:m,page:c,page_size:s,sort_field:u,sort_direction:k});if(!e.current)return;N(h.pagination),C(h.documents),$(h.status_counts);const S={statuses:{processed:h.documents.filter(A=>A.status==="processed"),processing:h.documents.filter(A=>A.status==="processing"),pending:h.documents.filter(A=>A.status==="pending"),failed:h.documents.filter(A=>A.status==="failed")}};h.pagination.total_count>0?b(S):b(null)}catch(v){e.current&&O.error(i("documentPanel.documentManager.errors.loadFailed",{error:ae(v)}))}finally{e.current&&P(!1)}},[u,k,i]),Y=o.useCallback(async()=>{await oe(w.page,w.page_size,D)},[oe,w.page,w.page_size,D]),J=o.useRef(void 0),fe=o.useRef(null),Q=o.useCallback(()=>{fe.current&&(clearInterval(fe.current),fe.current=null)},[]),W=o.useCallback(c=>{Q(),fe.current=setInterval(async()=>{try{e.current&&await Y()}catch(s){e.current&&O.error(i("documentPanel.documentManager.errors.scanProgressFailed",{error:ae(s)}))}},c)},[Y,i,Q]),_e=o.useCallback(async()=>{try{if(!e.current)return;const{status:c,message:s,track_id:m}=await Xt();if(!e.current)return;O.message(s||c),Ce.getState().resetHealthCheckTimerDelayed(1e3),W(2e3),setTimeout(()=>{if(e.current&&f==="documents"&&r){const h=(g.processing||0)>0||(g.pending||0)>0?5e3:3e4;W(h)}},15e3)}catch(c){e.current&&O.error(i("documentPanel.documentManager.errors.scanFailed",{error:ae(c)}))}},[i,W,f,r,g]),X=o.useCallback(c=>{c!==w.page_size&&(j(c),L({all:1,processed:1,processing:1,pending:1,failed:1}),N(s=>({...s,page:1,page_size:c})))},[w.page_size,j]),xe=o.useCallback(async()=>{try{P(!0);const c={status_filter:D==="all"?null:D,page:1,page_size:w.page_size,sort_field:u,sort_direction:k},s=await wa(c);if(!e.current)return;if(s.pagination.total_count<w.page_size&&w.page_size!==10)X(10);else{N(s.pagination),C(s.documents),$(s.status_counts);const m={statuses:{processed:s.documents.filter(v=>v.status==="processed"),processing:s.documents.filter(v=>v.status==="processing"),pending:s.documents.filter(v=>v.status==="pending"),failed:s.documents.filter(v=>v.status==="failed")}};s.pagination.total_count>0?b(m):b(null)}}catch(c){e.current&&O.error(i("documentPanel.documentManager.errors.loadFailed",{error:ae(c)}))}finally{e.current&&P(!1)}},[D,w.page_size,u,k,X,i]);o.useEffect(()=>{if(J.current!==void 0&&J.current!==p&&f==="documents"&&r&&e.current){xe();const s=(g.processing||0)>0||(g.pending||0)>0?5e3:3e4;W(s)}J.current=p},[p,f,r,xe,g.processing,g.pending,W]),o.useEffect(()=>{if(f!=="documents"||!r){Q();return}const s=(g.processing||0)>0||(g.pending||0)>0?5e3:3e4;return W(s),()=>{Q()}},[r,i,f,g,W,Q]),o.useEffect(()=>{var m,v,h,S,A,se,ye,je;if(!d)return;const c={processed:((v=(m=d==null?void 0:d.statuses)==null?void 0:m.processed)==null?void 0:v.length)||0,processing:((S=(h=d==null?void 0:d.statuses)==null?void 0:h.processing)==null?void 0:S.length)||0,pending:((se=(A=d==null?void 0:d.statuses)==null?void 0:A.pending)==null?void 0:se.length)||0,failed:((je=(ye=d==null?void 0:d.statuses)==null?void 0:ye.failed)==null?void 0:je.length)||0};Object.keys(c).some(we=>c[we]!==ze.current[we])&&e.current&&Ce.getState().check(),ze.current=c},[d]);const be=o.useCallback(c=>{c!==w.page&&(L(s=>({...s,[D]:c})),N(s=>({...s,page:c})))},[w.page,D]),Z=o.useCallback(c=>{if(c===D)return;L(m=>({...m,[D]:w.page}));const s=ue[c];G(c),N(m=>({...m,page:s}))},[D,w.page,ue]),Ke=o.useCallback(async()=>{R([]),Ce.getState().resetHealthCheckTimerDelayed(1e3),W(2e3)},[W]),We=o.useCallback(async()=>{if(Q(),$({all:0,processed:0,processing:0,pending:0,failed:0}),e.current)try{await Y()}catch(c){console.error("Error fetching documents after clear:",c)}f==="documents"&&r&&e.current&&W(3e4)},[Q,$,Y,f,r,W]);return o.useEffect(()=>{if(u==="id"||u==="file_path"){const c=x?"file_path":"id";u!==c&&z(c)}},[x,u]),o.useEffect(()=>{R([])},[w.page,D,u,k]),o.useEffect(()=>{f==="documents"&&oe(w.page,w.page_size,D)},[f,w.page,w.page_size,D,u,k,oe]),t.jsxs(oa,{className:"!rounded-none !overflow-hidden flex flex-col h-full min-h-0",children:[t.jsx(ka,{className:"py-2 px-6",children:t.jsx(sa,{className:"text-lg",children:i("documentPanel.documentManager.title")})}),t.jsxs(Da,{className:"flex-1 flex flex-col min-h-0 overflow-auto",children:[t.jsxs("div",{className:"flex justify-between items-center gap-2 mb-2",children:[t.jsxs("div",{className:"flex gap-2",children:[t.jsxs(T,{variant:"outline",onClick:_e,side:"bottom",tooltip:i("documentPanel.documentManager.scanTooltip"),size:"sm",children:[t.jsx(Zt,{})," ",i("documentPanel.documentManager.scanButton")]}),t.jsxs(T,{variant:"outline",onClick:()=>n(!0),side:"bottom",tooltip:i("documentPanel.documentManager.pipelineStatusTooltip"),size:"sm",className:E(p&&"pipeline-busy"),children:[t.jsx(en,{})," ",i("documentPanel.documentManager.pipelineStatusButton")]})]}),w.total_pages>1&&t.jsx(fi,{currentPage:w.page,totalPages:w.total_pages,pageSize:w.page_size,totalCount:w.total_count,onPageChange:be,onPageSizeChange:X,isLoading:I,compact:!0}),t.jsxs("div",{className:"flex gap-2",children:[U&&t.jsx(ui,{selectedDocIds:H,onDocumentsDeleted:Ke}),U&&V?(()=>{const c=ie(),s=c.icon;return t.jsxs(T,{variant:"outline",size:"sm",onClick:c.action,side:"bottom",tooltip:c.text,children:[t.jsx(s,{className:"h-4 w-4"}),c.text]})})():U?null:t.jsx(mi,{onDocumentsCleared:We}),t.jsx(di,{onDocumentsUploaded:Y}),t.jsx(xi,{open:a,onOpenChange:n})]})]}),t.jsxs(oa,{className:"flex-1 flex flex-col border rounded-md min-h-0 mb-2",children:[t.jsxs(ka,{className:"flex-none py-2 px-4",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx(sa,{children:i("documentPanel.documentManager.uploadedTitle")}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("div",{className:"flex gap-1",dir:l.dir(),children:[t.jsxs(T,{size:"sm",variant:D==="all"?"secondary":"outline",onClick:()=>Z("all"),disabled:I,className:E(D==="all"&&"bg-gray-100 dark:bg-gray-900 font-medium border border-gray-400 dark:border-gray-500 shadow-sm"),children:[i("documentPanel.documentManager.status.all")," (",g.all||de.all,")"]}),t.jsxs(T,{size:"sm",variant:D==="processed"?"secondary":"outline",onClick:()=>Z("processed"),disabled:I,className:E((g.PROCESSED||g.processed||de.processed)>0?"text-green-600":"text-gray-500",D==="processed"&&"bg-green-100 dark:bg-green-900/30 font-medium border border-green-400 dark:border-green-600 shadow-sm"),children:[i("documentPanel.documentManager.status.completed")," (",g.PROCESSED||g.processed||0,")"]}),t.jsxs(T,{size:"sm",variant:D==="processing"?"secondary":"outline",onClick:()=>Z("processing"),disabled:I,className:E((g.PROCESSING||g.processing||de.processing)>0?"text-blue-600":"text-gray-500",D==="processing"&&"bg-blue-100 dark:bg-blue-900/30 font-medium border border-blue-400 dark:border-blue-600 shadow-sm"),children:[i("documentPanel.documentManager.status.processing")," (",g.PROCESSING||g.processing||0,")"]}),t.jsxs(T,{size:"sm",variant:D==="pending"?"secondary":"outline",onClick:()=>Z("pending"),disabled:I,className:E((g.PENDING||g.pending||de.pending)>0?"text-yellow-600":"text-gray-500",D==="pending"&&"bg-yellow-100 dark:bg-yellow-900/30 font-medium border border-yellow-400 dark:border-yellow-600 shadow-sm"),children:[i("documentPanel.documentManager.status.pending")," (",g.PENDING||g.pending||0,")"]}),t.jsxs(T,{size:"sm",variant:D==="failed"?"secondary":"outline",onClick:()=>Z("failed"),disabled:I,className:E((g.FAILED||g.failed||de.failed)>0?"text-red-600":"text-gray-500",D==="failed"&&"bg-red-100 dark:bg-red-900/30 font-medium border border-red-400 dark:border-red-600 shadow-sm"),children:[i("documentPanel.documentManager.status.failed")," (",g.FAILED||g.failed||0,")"]})]}),t.jsx(T,{variant:"ghost",size:"sm",onClick:xe,disabled:I,side:"bottom",tooltip:i("documentPanel.documentManager.refreshTooltip"),children:t.jsx(an,{className:"h-4 w-4"})})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("label",{htmlFor:"toggle-filename-btn",className:"text-sm text-gray-500",children:i("documentPanel.documentManager.fileNameLabel")}),t.jsx(T,{id:"toggle-filename-btn",variant:"outline",size:"sm",onClick:()=>_(!x),className:"border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800",children:i(x?"documentPanel.documentManager.hideButton":"documentPanel.documentManager.showButton")})]})]}),t.jsx(at,{"aria-hidden":"true",className:"hidden",children:i("documentPanel.documentManager.uploadedDescription")})]}),t.jsxs(Da,{className:"flex-1 relative p-0",ref:Ee,children:[!d&&t.jsx("div",{className:"absolute inset-0 p-0",children:t.jsx(ln,{title:i("documentPanel.documentManager.emptyTitle"),description:i("documentPanel.documentManager.emptyDescription")})}),d&&t.jsx("div",{className:"absolute inset-0 flex flex-col p-0",children:t.jsx("div",{className:"absolute inset-[-1px] flex flex-col p-0 border rounded-md border-gray-200 dark:border-gray-700 overflow-hidden",children:t.jsxs(ct,{className:"w-full",children:[t.jsx(pt,{className:"sticky top-0 bg-background z-10 shadow-sm",children:t.jsxs(da,{className:"border-b bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/75 shadow-[inset_0_-1px_0_rgba(0,0,0,0.1)]",children:[t.jsx(le,{onClick:()=>te("id"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:t.jsxs("div",{className:"flex items-center",children:[i(x?"documentPanel.documentManager.columns.fileName":"documentPanel.documentManager.columns.id"),(u==="id"&&!x||u==="file_path"&&x)&&t.jsx("span",{className:"ml-1",children:k==="asc"?t.jsx(Qe,{size:14}):t.jsx(Xe,{size:14})})]})}),t.jsx(le,{children:i("documentPanel.documentManager.columns.summary")}),t.jsx(le,{children:i("documentPanel.documentManager.columns.status")}),t.jsx(le,{children:i("documentPanel.documentManager.columns.length")}),t.jsx(le,{children:i("documentPanel.documentManager.columns.chunks")}),t.jsx(le,{onClick:()=>te("created_at"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:t.jsxs("div",{className:"flex items-center",children:[i("documentPanel.documentManager.columns.created"),u==="created_at"&&t.jsx("span",{className:"ml-1",children:k==="asc"?t.jsx(Qe,{size:14}):t.jsx(Xe,{size:14})})]})}),t.jsx(le,{onClick:()=>te("updated_at"),className:"cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-800 select-none",children:t.jsxs("div",{className:"flex items-center",children:[i("documentPanel.documentManager.columns.updated"),u==="updated_at"&&t.jsx("span",{className:"ml-1",children:k==="asc"?t.jsx(Qe,{size:14}):t.jsx(Xe,{size:14})})]})}),t.jsx(le,{className:"w-16 text-center",children:i("documentPanel.documentManager.columns.select")})]})}),t.jsx(dt,{className:"text-sm overflow-auto",children:pe&&pe.map(c=>t.jsxs(da,{children:[t.jsx(re,{className:"truncate font-mono overflow-visible max-w-[250px]",children:x?t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[t.jsx("div",{className:"truncate",children:ia(c,30)}),t.jsx("div",{className:"invisible group-hover:visible tooltip",children:c.file_path})]}),t.jsx("div",{className:"text-xs text-gray-500",children:c.id})]}):t.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[t.jsx("div",{className:"truncate",children:c.id}),t.jsx("div",{className:"invisible group-hover:visible tooltip",children:c.file_path})]})}),t.jsx(re,{className:"max-w-xs min-w-45 truncate overflow-visible",children:t.jsxs("div",{className:"group relative overflow-visible tooltip-container",children:[t.jsx("div",{className:"truncate",children:c.content_summary}),t.jsx("div",{className:"invisible group-hover:visible tooltip",children:c.content_summary})]})}),t.jsxs(re,{children:[c.status==="processed"&&t.jsx("span",{className:"text-green-600",children:i("documentPanel.documentManager.status.completed")}),c.status==="processing"&&t.jsx("span",{className:"text-blue-600",children:i("documentPanel.documentManager.status.processing")}),c.status==="pending"&&t.jsx("span",{className:"text-yellow-600",children:i("documentPanel.documentManager.status.pending")}),c.status==="failed"&&t.jsx("span",{className:"text-red-600",children:i("documentPanel.documentManager.status.failed")}),c.error_msg&&t.jsx("span",{className:"ml-2 text-red-500",title:c.error_msg,children:"⚠️"})]}),t.jsx(re,{children:c.content_length??"-"}),t.jsx(re,{children:c.chunks_count??"-"}),t.jsx(re,{className:"truncate",children:new Date(c.created_at).toLocaleString()}),t.jsx(re,{className:"truncate",children:new Date(c.updated_at).toLocaleString()}),t.jsx(re,{className:"text-center",children:t.jsx(ot,{checked:H.includes(c.id),onCheckedChange:s=>De(c.id,s===!0),className:"mx-auto"})})]},c.id))})]})})})]})]})]})]})}export{ji as D,Na as S,ra as a,za as b,ca as c,yi as d,pa as e};
