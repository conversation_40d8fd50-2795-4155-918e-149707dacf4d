import{_ as e,l as o,K as i,e as n,L as p}from"./mermaid-vendor-D-8fVEyy.js";import{p as m}from"./treemap-75Q7IDZK-B6UbH8A6.js";import"./feature-graph-DtzGBYZt.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";import"./_baseUniq-B-LJwYfV.js";import"./_basePickBy-DeaQWhXJ.js";import"./clone-DbWVT42G.js";var g={parse:e(async r=>{const a=await m("info",r);o.debug(a)},"parse")},v={version:p.version+""},d=e(()=>v.version,"getVersion"),c={getVersion:d},l=e((r,a,s)=>{o.debug(`rendering info diagram
`+r);const t=i(a);n(t,100,400,!0),t.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${s}`)},"draw"),f={draw:l},L={parser:g,db:c,renderer:f};export{L as diagram};
