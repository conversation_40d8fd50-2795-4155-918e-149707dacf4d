# 压测脚本修复说明

## 修复的问题

### 1. 健康检查端点错误 ✅
**问题**: 使用了错误的健康检查端点 `/health`
**修复**: 更正为 `/api/v1/health`
**影响**: 确保服务可用性检查能正常工作

### 2. 缺少详细的错误处理和日志 ✅
**问题**: 请求失败时缺少详细的错误信息
**修复**: 
- 添加了详细的请求和响应日志
- 改进了异常处理，包含错误类型和详细信息
- 添加了调试模式支持

### 3. 连接测试不充分 ✅
**问题**: 只检查健康状态，没有测试实际的API功能
**修复**: 
- 添加了基本查询测试 `_test_basic_query()`
- 在服务可用性检查中测试实际的查询功能

### 4. 缺少调试和诊断工具 ✅
**问题**: 难以诊断连接问题
**修复**: 
- 添加了 `--debug` 参数启用详细日志
- 添加了 `--test-connection` 参数仅测试连接
- 创建了独立的连接测试脚本 `test_connection.py`

### 5. 请求头和参数问题 ✅
**问题**: 可能缺少必要的HTTP头
**修复**: 
- 明确设置 `Content-Type: application/json`
- 改进了请求参数的日志记录

## 使用修复后的脚本

### 1. 基本连接测试
```bash
# 测试连接是否正常
python tests/test_connection.py

# 测试指定URL
python tests/test_connection.py --url http://localhost:8002

# 包含性能测试
python tests/test_connection.py --performance --requests 20
```

### 2. 压测脚本连接测试
```bash
# 仅测试连接，不运行压测
python tests/stress_test.py --test-connection

# 启用调试模式
python tests/stress_test.py --debug --test-connection

# 调试模式下运行小规模测试
python tests/stress_test.py --debug --users 5 --duration 60
```

### 3. 正常压力测试
```bash
# 小规模测试
python tests/stress_test.py --users 10 --duration 120

# 标准测试
python tests/stress_test.py --users 100 --duration 600

# 高负载测试
python tests/stress_test.py --users 500 --duration 1800
```

## 故障排除步骤

### 步骤1: 验证服务运行状态
```bash
# 检查服务是否在运行
curl http://localhost:8002/api/v1/health

# 或使用连接测试工具
python tests/test_connection.py
```

### 步骤2: 检查网络连接
```bash
# 检查端口是否开放
telnet localhost 8002

# 检查进程是否在运行
netstat -an | grep 8002
```

### 步骤3: 查看服务日志
检查GuiXiaoXiRag服务的日志文件，查看是否有错误信息。

### 步骤4: 使用调试模式
```bash
# 启用详细日志
python tests/stress_test.py --debug --test-connection
```

## 常见问题解决

### 问题1: 连接被拒绝
**症状**: `Connection refused` 错误
**解决**: 
1. 确认服务正在运行
2. 检查端口号是否正确
3. 检查防火墙设置

### 问题2: 超时错误
**症状**: `Timeout` 错误
**解决**: 
1. 增加超时时间 `--timeout 60`
2. 检查服务器负载
3. 减少并发用户数

### 问题3: HTTP 404错误
**症状**: 返回404状态码
**解决**: 
1. 检查API端点是否正确
2. 确认服务版本和API版本匹配

### 问题4: HTTP 500错误
**症状**: 返回500状态码
**解决**: 
1. 查看服务端日志
2. 检查服务配置
3. 确认依赖服务正常

## 新增功能

### 1. 调试模式
- 使用 `--debug` 启用详细日志
- 显示请求和响应的详细信息
- 记录每个用户的请求状态

### 2. 连接测试模式
- 使用 `--test-connection` 仅测试连接
- 验证所有主要API端点
- 不运行实际的压力测试

### 3. 独立连接测试工具
- `test_connection.py` 提供独立的连接测试
- 支持性能测试模式
- 提供详细的诊断信息

### 4. 改进的错误报告
- 详细的错误类型和消息
- 请求URL和参数的记录
- 建议的解决方案

## 验证修复

运行以下命令验证修复是否成功：

```bash
# 1. 基本连接测试
python tests/test_connection.py

# 2. 压测连接测试
python tests/stress_test.py --test-connection

# 3. 小规模调试测试
python tests/stress_test.py --debug --users 3 --duration 30

# 4. 如果以上都成功，运行正常压测
python tests/stress_test.py --users 50 --duration 300
```

如果所有测试都通过，说明修复成功，可以正常使用压测脚本。

## 监控建议

在运行压测时，建议同时监控：
1. 服务端日志
2. 系统资源使用情况
3. 网络连接状态
4. 压测脚本的输出日志

这样可以及时发现和解决问题。
