# GuiXiaoXiRag 压力测试脚本使用指南

## 概述

优化后的压力测试脚本 `stress_test.py` 专门设计用于测试 GuiXiaoXiRag 系统在高并发场景下的性能，特别针对 cs_college 知识库的多种查询模式和可变文本长度进行全面测试。

## 主要特性

### 🎯 查询模式测试
- **local**: 本地模式 - 专注于上下文相关信息
- **global**: 全局模式 - 利用全局知识
- **hybrid**: 混合模式 - 结合本地和全局检索方法（推荐）
- **naive**: 朴素模式 - 执行基本搜索
- **mix**: 混合模式 - 整合知识图谱和向量检索
- **bypass**: 绕过模式 - 直接返回结果

### 📏 可变文本长度测试
- **short**: 50-200 tokens - 简短查询
- **medium**: 200-800 tokens - 中等长度查询
- **long**: 800-2000 tokens - 长查询
- **very_long**: 2000-5000 tokens - 很长查询
- **ultra_long**: 5000-8000 tokens - 超长查询

### 🏫 专项测试
- 专门针对 **cs_college** 知识库
- 确保每个用户测试不同的查询模式和文本长度
- 提供详细的性能分析和统计

### 🚦 用户频率控制
- **智能频率控制**: 确保同一用户不会频繁请求
- **多用户并发**: 不同用户可以同时访问，互不影响
- **动态间隔调整**: 根据用户请求历史调整间隔
- **可配置参数**: 支持自定义最小/最大请求间隔
- **可选功能**: 支持完全禁用频率控制

## 使用方法

### 基础使用

```bash
# 默认配置 (100用户, 10分钟, 70%智能查询)
python tests/stress_test.py

# 查看帮助
python tests/stress_test.py --help
```

### 常用测试场景

```bash
# 快速测试 (50用户, 5分钟)
python tests/stress_test.py --users 50 --duration 300

# 标准测试 (200用户, 15分钟)
python tests/stress_test.py --users 200 --duration 900

# 高负载测试 (1000用户, 30分钟)
python tests/stress_test.py --users 1000 --duration 1800

# 极限测试 (2000用户, 1小时)
python tests/stress_test.py --users 2000 --duration 3600

# 专项智能查询测试 (500用户, 90%智能查询)
python tests/stress_test.py --users 500 --duration 900 --query-ratio 0.9 --qa-query-ratio 0.05
```

### 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--url` | http://localhost:8002 | 服务器URL |
| `--users` | 100 | 并发用户数 |
| `--duration` | 600 | 测试持续时间(秒) |
| `--ramp-up` | 60 | 负载增加时间(秒) |
| `--query-ratio` | 0.70 | 智能查询比例 |
| `--qa-query-ratio` | 0.15 | 问答查询比例 |
| `--qa-batch-ratio` | 0.05 | 批量查询比例 |
| `--qa-create-ratio` | 0.05 | 创建问答对比例 |
| `--pool-size` | 500 | 连接池大小 |
| `--timeout` | 30 | 请求超时时间(秒) |
| `--min-interval` | 1.0 | 用户最小请求间隔(秒) |
| `--max-interval` | 5.0 | 用户最大请求间隔(秒) |
| `--disable-rate-limiting` | False | 禁用用户频率控制 |

## 测试报告

### 报告内容

测试完成后会生成详细的报告，包括：

1. **测试概览**
   - 测试时长、并发用户数
   - 总请求数、成功率、吞吐量

2. **响应时间统计**
   - 平均、最小、最大响应时间
   - P50、P90、P95、P99 百分位数

3. **查询模式性能统计**
   - 每种查询模式的请求数和响应时间
   - 成功率对比

4. **文本长度性能统计**
   - 不同长度类别的性能表现
   - Token范围分析

5. **系统资源使用**
   - CPU、内存使用情况
   - 网络连接数

### 报告文件

- `stress_test_report_XXXusers_YYYYMMDD_HHMMSS.json` - 详细报告
- `stress_test_report_XXXusers_YYYYMMDD_HHMMSS_summary.json` - 简化报告
- `stress_test_YYYYMMDD_HHMMSS.log` - 测试日志

## 功能验证

运行功能验证脚本：

```bash
# 验证查询模式和文本长度功能
python tests/test_query_modes.py
```

## 性能优化建议

### 系统配置
- 确保有足够的内存 (推荐8GB+)
- 调整系统文件描述符限制
- 优化网络连接参数

### 测试配置
- 根据系统性能调整并发用户数
- 合理设置连接池大小
- 监控系统资源使用情况

### 结果分析
- 关注P95响应时间，而不仅仅是平均值
- 比较不同查询模式的性能差异
- 分析文本长度对响应时间的影响

## 故障排除

### 常见问题

1. **连接超时**
   - 增加 `--timeout` 参数
   - 减少并发用户数
   - 检查网络连接

2. **内存不足**
   - 减少 `--buffer-size` 参数
   - 降低并发用户数
   - 增加系统内存

3. **服务不可用**
   - 检查服务是否正常运行
   - 验证URL和端口
   - 查看服务日志

### 调试模式

```bash
# 启用详细日志
python tests/stress_test.py --users 10 --duration 60 --quiet false
```

## 注意事项

1. **测试环境**
   - 确保测试环境与生产环境配置相似
   - 避免在生产环境直接运行高负载测试

2. **资源监控**
   - 实时监控系统资源使用
   - 注意磁盘空间和网络带宽

3. **数据安全**
   - 测试数据会写入系统，注意数据清理
   - 备份重要数据

4. **测试时机**
   - 避免在系统高峰期进行测试
   - 预留足够的测试时间

## 扩展功能

脚本支持进一步扩展：

- 添加新的查询模式
- 自定义文本长度范围
- 集成更多性能指标
- 支持分布式测试

---

**版本**: 2.0.0  
**更新时间**: 2024-01-01  
**维护者**: GuiXiaoXiRag Team
