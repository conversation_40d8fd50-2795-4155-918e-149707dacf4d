#!/usr/bin/env python3
"""
连接测试脚本
用于验证压测脚本能否正常连接到后端服务
"""

import asyncio
import aiohttp
import sys
import json
import time
from datetime import datetime


async def test_connection(base_url="http://localhost:8002"):
    """测试与后端服务的连接"""
    print(f"🔍 开始测试连接到: {base_url}")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 创建HTTP会话
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        
        # 测试1: 健康检查
        print("📡 测试1: 健康检查")
        try:
            async with session.get(f"{base_url}/api/v1/health") as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"   响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    print("   ✅ 健康检查通过")
                else:
                    print(f"   ❌ 健康检查失败: HTTP {response.status}")
                    return False
        except Exception as e:
            print(f"   ❌ 健康检查异常: {e}")
            return False
        
        print()
        
        # 测试2: 智能查询
        print("📡 测试2: 智能查询")
        try:
            query_payload = {
                "query": "什么是计算机科学？",
                "mode": "hybrid",
                "knowledge_base": "cs_college"
            }
            
            async with session.post(
                f"{base_url}/api/v1/query",
                json=query_payload,
                headers={'Content-Type': 'application/json'}
            ) as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    success = data.get("success", False)
                    print(f"   成功: {success}")
                    if success:
                        print("   ✅ 智能查询通过")
                    else:
                        print(f"   ⚠️ 智能查询返回失败: {data.get('message', 'Unknown error')}")
                else:
                    print(f"   ❌ 智能查询失败: HTTP {response.status}")
        except Exception as e:
            print(f"   ❌ 智能查询异常: {e}")
        
        print()
        
        # 测试3: 问答系统健康检查
        print("📡 测试3: 问答系统健康检查")
        try:
            async with session.get(f"{base_url}/api/v1/qa/health") as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"   响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    print("   ✅ 问答系统健康检查通过")
                else:
                    print(f"   ⚠️ 问答系统健康检查失败: HTTP {response.status}")
        except Exception as e:
            print(f"   ❌ 问答系统健康检查异常: {e}")
        
        print()
        
        # 测试4: 问答查询
        print("📡 测试4: 问答查询")
        try:
            qa_payload = {
                "question": "什么是人工智能？",
                "top_k": 3,
                "min_similarity": 0.8
            }
            
            async with session.post(
                f"{base_url}/api/v1/qa/query",
                json=qa_payload,
                headers={'Content-Type': 'application/json'}
            ) as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    success = data.get("success", False)
                    found = data.get("found", False)
                    print(f"   成功: {success}, 找到答案: {found}")
                    if success:
                        print("   ✅ 问答查询通过")
                    else:
                        print(f"   ⚠️ 问答查询返回失败: {data.get('message', 'Unknown error')}")
                else:
                    print(f"   ❌ 问答查询失败: HTTP {response.status}")
        except Exception as e:
            print(f"   ❌ 问答查询异常: {e}")
        
        print()
        
        # 测试5: 系统状态
        print("📡 测试5: 系统状态")
        try:
            async with session.get(f"{base_url}/api/v1/system/status") as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"   系统状态: {data.get('status', 'unknown')}")
                    print("   ✅ 系统状态查询通过")
                else:
                    print(f"   ⚠️ 系统状态查询失败: HTTP {response.status}")
        except Exception as e:
            print(f"   ❌ 系统状态查询异常: {e}")
    
    print("=" * 60)
    print("✅ 连接测试完成")
    return True


async def test_performance(base_url="http://localhost:8002", num_requests=10):
    """测试基本性能"""
    print(f"\n🚀 开始性能测试: {num_requests} 个请求")
    print("=" * 60)
    
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        
        response_times = []
        success_count = 0
        
        for i in range(num_requests):
            start_time = time.time()
            
            try:
                query_payload = {
                    "query": f"测试查询 {i+1}: 什么是计算机科学？",
                    "mode": "hybrid",
                    "knowledge_base": "cs_college"
                }
                
                async with session.post(
                    f"{base_url}/api/v1/query",
                    json=query_payload,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    response_time = time.time() - start_time
                    response_times.append(response_time)
                    
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success", False):
                            success_count += 1
                    
                    print(f"   请求 {i+1:2}: {response.status} | {response_time:.3f}s")
                    
            except Exception as e:
                response_time = time.time() - start_time
                response_times.append(response_time)
                print(f"   请求 {i+1:2}: 异常 | {response_time:.3f}s | {e}")
        
        # 统计结果
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            success_rate = success_count / num_requests * 100
            
            print(f"\n📊 性能统计:")
            print(f"   总请求数: {num_requests}")
            print(f"   成功请求: {success_count}")
            print(f"   成功率: {success_rate:.1f}%")
            print(f"   平均响应时间: {avg_time:.3f}s")
            print(f"   最快响应时间: {min_time:.3f}s")
            print(f"   最慢响应时间: {max_time:.3f}s")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="连接测试工具")
    parser.add_argument("--url", default="http://localhost:8002", help="服务器URL")
    parser.add_argument("--performance", action="store_true", help="运行性能测试")
    parser.add_argument("--requests", type=int, default=10, help="性能测试请求数")
    
    args = parser.parse_args()
    
    print("🔧 GuiXiaoXiRag 连接测试工具")
    print(f"🎯 目标服务器: {args.url}")
    
    # 基本连接测试
    success = await test_connection(args.url)
    
    if success and args.performance:
        # 性能测试
        await test_performance(args.url, args.requests)
    
    if success:
        print("\n💡 建议:")
        print("   1. 如果所有测试都通过，可以运行压力测试:")
        print(f"      python tests/stress_test.py --url {args.url} --test-connection")
        print("   2. 如果要运行完整压测:")
        print(f"      python tests/stress_test.py --url {args.url} --users 50 --duration 300")
    else:
        print("\n❌ 连接测试失败，请检查:")
        print("   1. 服务是否正在运行")
        print("   2. URL和端口是否正确")
        print("   3. 防火墙设置")
        print("   4. 网络连接")


if __name__ == "__main__":
    asyncio.run(main())
