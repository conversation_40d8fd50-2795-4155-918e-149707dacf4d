#!/usr/bin/env python3
"""
测试查询模式和可变文本长度功能
验证优化后的压测脚本的核心功能
"""

import sys
import os
import asyncio
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.stress_test import HighConcurrencyStressTest, TestConfig


def test_variable_length_queries():
    """测试可变长度查询生成"""
    print("🧪 测试可变长度查询生成...")
    
    config = TestConfig(
        base_url="http://localhost:8002",
        concurrent_users=10,
        test_duration=60,
        query_ratio=1.0,
        qa_query_ratio=0.0,
        qa_batch_ratio=0.0,
        qa_create_ratio=0.0,
        health_ratio=0.0
    )
    
    tester = HighConcurrencyStressTest(config)
    
    print(f"✅ 生成的查询类别数: {len(tester.variable_length_queries)}")
    
    total_queries = 0
    for category, queries in tester.variable_length_queries.items():
        total_queries += len(queries)
        print(f"\n📏 {category} 类别:")
        print(f"   - 查询数量: {len(queries)}")
        if queries:
            sample_query = queries[0]
            estimated_tokens = tester._estimate_tokens(sample_query)
            print(f"   - 示例token数: {estimated_tokens}")
            print(f"   - 示例内容: {sample_query[:100]}...")
    
    print(f"\n📊 总查询数: {total_queries}")
    print(f"🎯 查询模式: {tester.query_modes}")
    print(f"🎯 性能模式: {tester.performance_modes}")


def test_user_specific_configs():
    """测试用户特定配置生成"""
    print("\n🧪 测试用户特定配置生成...")
    
    config = TestConfig(
        base_url="http://localhost:8002",
        concurrent_users=10,
        test_duration=60
    )
    
    tester = HighConcurrencyStressTest(config)
    
    print("📊 前10个用户的查询配置示例:")
    print("用户ID | 查询模式 | 性能模式 | 文本长度 | Token数")
    print("-" * 60)
    
    for user_id in range(10):
        for request_count in range(3):  # 每个用户测试3次请求
            config_data = tester.get_user_specific_query_config(user_id, request_count)
            print(f"{user_id:6} | {config_data['query_mode']:8} | {config_data['performance_mode']:8} | "
                  f"{config_data['length_category']:10} | {config_data['estimated_tokens']:6}")


def test_query_mode_coverage():
    """测试查询模式覆盖率"""
    print("\n🧪 测试查询模式覆盖率...")
    
    config = TestConfig(
        base_url="http://localhost:8002",
        concurrent_users=20,
        test_duration=60
    )
    
    tester = HighConcurrencyStressTest(config)
    
    # 统计每个用户在多次请求中使用的模式
    mode_usage = {}
    length_usage = {}
    
    for user_id in range(20):
        user_modes = set()
        user_lengths = set()
        
        for request_count in range(10):  # 每个用户10次请求
            config_data = tester.get_user_specific_query_config(user_id, request_count)
            user_modes.add(config_data['query_mode'])
            user_lengths.add(config_data['length_category'])
        
        mode_usage[user_id] = user_modes
        length_usage[user_id] = user_lengths
    
    # 分析覆盖率
    all_modes = set(tester.query_modes)
    all_lengths = set(tester.variable_length_queries.keys())
    
    mode_coverage = []
    length_coverage = []
    
    for user_id in range(20):
        mode_coverage.append(len(mode_usage[user_id]) / len(all_modes))
        length_coverage.append(len(length_usage[user_id]) / len(all_lengths))
    
    avg_mode_coverage = sum(mode_coverage) / len(mode_coverage)
    avg_length_coverage = sum(length_coverage) / len(length_coverage)
    
    print(f"📊 查询模式覆盖率分析 (20用户, 每用户10次请求):")
    print(f"   • 平均模式覆盖率: {avg_mode_coverage:.1%}")
    print(f"   • 平均长度覆盖率: {avg_length_coverage:.1%}")
    print(f"   • 总查询模式数: {len(all_modes)}")
    print(f"   • 总长度类别数: {len(all_lengths)}")
    
    # 显示每个用户的覆盖情况
    print(f"\n📋 用户覆盖详情:")
    for user_id in range(min(10, 20)):  # 只显示前10个用户
        modes = sorted(mode_usage[user_id])
        lengths = sorted(length_usage[user_id])
        print(f"   用户{user_id:2}: 模式{len(modes)}/{len(all_modes)} {modes} | 长度{len(lengths)}/{len(all_lengths)} {lengths}")


def test_token_estimation():
    """测试token估算功能"""
    print("\n🧪 测试token估算功能...")
    
    config = TestConfig()
    tester = HighConcurrencyStressTest(config)
    
    test_texts = [
        "什么是人工智能？",
        "请详细介绍机器学习的基本概念和核心原理，包括相关的技术细节、应用场景和发展现状。",
        "关于深度学习这个重要的技术领域，我想了解以下几个方面的内容：1. 深度学习的核心概念和基本原理是什么？2. 深度学习在当前技术发展中处于什么地位？3. 深度学习有哪些主要的应用领域和实际案例？请针对每个问题提供详细的分析和说明。"
    ]
    
    print("📊 Token估算测试:")
    for i, text in enumerate(test_texts):
        tokens = tester._estimate_tokens(text)
        print(f"   文本{i+1}: {tokens:3} tokens | {text[:50]}...")


def main():
    """主测试函数"""
    print("🚀 开始测试优化后的压测脚本功能")
    print("=" * 80)
    
    # 测试可变长度查询生成
    test_variable_length_queries()
    
    # 测试用户特定配置
    test_user_specific_configs()
    
    # 测试查询模式覆盖率
    test_query_mode_coverage()
    
    # 测试token估算
    test_token_estimation()
    
    print("\n" + "=" * 80)
    print("✅ 所有功能测试完成！")
    
    print("\n📋 优化后的压测脚本特性总结:")
    print("   🎯 支持6种查询模式: local, global, hybrid, naive, mix, bypass")
    print("   📏 支持5种文本长度: short(50-200), medium(200-800), long(800-2000), very_long(2000-5000), ultra_long(5000-8000)")
    print("   🏫 专门针对cs_college知识库进行测试")
    print("   👥 每个用户都会轮换使用所有查询模式和文本长度")
    print("   📊 详细的性能统计，包括模式、长度、token范围分析")
    print("   🔄 确定性的配置分配，保证测试覆盖率")
    
    print("\n🎯 推荐使用方式:")
    print("   • 快速测试: python tests/stress_test.py --users 50 --duration 300")
    print("   • 标准测试: python tests/stress_test.py --users 200 --duration 900")
    print("   • 高负载测试: python tests/stress_test.py --users 1000 --duration 1800")
    print("   • 极限测试: python tests/stress_test.py --users 2000 --duration 3600")


if __name__ == "__main__":
    main()
