#!/usr/bin/env python3
"""
测试用户频率控制功能
验证同一用户不会频繁请求，但多用户可以同时访问
"""

import asyncio
import time
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.stress_test import UserRateLimiter, TestConfig, HighConcurrencyStressTest


async def test_single_user_rate_limiting():
    """测试单用户频率控制"""
    print("🧪 测试单用户频率控制...")
    
    rate_limiter = UserRateLimiter(min_interval=2.0, max_interval=4.0)
    user_id = 1
    
    print(f"📊 用户{user_id}连续请求测试 (最小间隔2s, 最大间隔4s):")
    
    request_times = []
    for i in range(5):
        start_time = time.time()
        wait_time = await rate_limiter.wait_for_user_turn(user_id)
        actual_time = time.time()
        request_times.append(actual_time)
        
        print(f"   请求{i+1}: 等待{wait_time:.2f}s | 时间戳: {actual_time:.2f}")
    
    # 分析间隔
    print(f"\n📈 请求间隔分析:")
    for i in range(1, len(request_times)):
        interval = request_times[i] - request_times[i-1]
        print(f"   请求{i}到{i+1}: {interval:.2f}s")
    
    print("✅ 单用户频率控制测试完成\n")


async def test_multi_user_concurrent():
    """测试多用户并发访问"""
    print("🧪 测试多用户并发访问...")
    
    rate_limiter = UserRateLimiter(min_interval=1.5, max_interval=3.0)
    
    async def user_requests(user_id, num_requests=3):
        """模拟用户请求"""
        user_times = []
        for i in range(num_requests):
            start_time = time.time()
            wait_time = await rate_limiter.wait_for_user_turn(user_id)
            actual_time = time.time()
            user_times.append((actual_time, wait_time))
            print(f"   用户{user_id} 请求{i+1}: 等待{wait_time:.2f}s | 时间: {actual_time:.2f}")
        return user_times
    
    # 同时启动5个用户
    print(f"📊 5个用户同时发送请求:")
    start_time = time.time()
    
    tasks = []
    for user_id in range(1, 6):
        task = asyncio.create_task(user_requests(user_id, 3))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    end_time = time.time()
    
    print(f"\n📈 并发测试结果:")
    print(f"   总耗时: {end_time - start_time:.2f}s")
    print(f"   用户数: 5")
    print(f"   每用户请求数: 3")
    print(f"   总请求数: 15")
    
    # 分析每个用户的请求间隔
    for i, user_times in enumerate(results):
        user_id = i + 1
        if len(user_times) > 1:
            intervals = []
            for j in range(1, len(user_times)):
                interval = user_times[j][0] - user_times[j-1][0]
                intervals.append(interval)
            avg_interval = sum(intervals) / len(intervals)
            print(f"   用户{user_id} 平均间隔: {avg_interval:.2f}s")
    
    print("✅ 多用户并发测试完成\n")


async def test_rate_limiter_stats():
    """测试频率控制器统计功能"""
    print("🧪 测试频率控制器统计功能...")
    
    rate_limiter = UserRateLimiter(min_interval=1.0, max_interval=2.0)
    
    # 模拟多个用户的请求
    for user_id in range(1, 4):
        for _ in range(3):
            await rate_limiter.wait_for_user_turn(user_id)
    
    stats = rate_limiter.get_user_stats()
    print(f"📊 统计信息:")
    print(f"   总用户数: {stats['total_users']}")
    print(f"   总请求数: {stats['total_requests']}")
    print(f"   平均每用户请求数: {stats['avg_requests_per_user']:.1f}")
    print(f"   最小间隔: {stats['min_interval']}s")
    print(f"   最大间隔: {stats['max_interval']}s")
    
    print("✅ 统计功能测试完成\n")


async def test_stress_test_integration():
    """测试与压测脚本的集成"""
    print("🧪 测试与压测脚本的集成...")
    
    # 创建测试配置
    config = TestConfig(
        base_url="http://localhost:8002",
        concurrent_users=3,
        test_duration=10,
        min_request_interval=1.0,
        max_request_interval=2.0,
        user_session_simulation=True
    )
    
    print(f"📊 配置信息:")
    print(f"   并发用户: {config.concurrent_users}")
    print(f"   测试时长: {config.test_duration}s")
    print(f"   最小间隔: {config.min_request_interval}s")
    print(f"   最大间隔: {config.max_request_interval}s")
    print(f"   用户会话模拟: {config.user_session_simulation}")
    
    # 创建压测器实例
    tester = HighConcurrencyStressTest(config)
    
    # 测试频率控制器
    print(f"\n🔧 测试频率控制器初始化:")
    print(f"   最小间隔: {tester.rate_limiter.min_interval}s")
    print(f"   最大间隔: {tester.rate_limiter.max_interval}s")
    
    # 模拟几个用户请求
    print(f"\n📡 模拟用户请求:")
    for user_id in range(1, 4):
        start_time = time.time()
        wait_time = await tester.rate_limiter.wait_for_user_turn(user_id)
        print(f"   用户{user_id}: 等待{wait_time:.2f}s")
    
    # 获取统计信息
    stats = tester.rate_limiter.get_user_stats()
    print(f"\n📊 最终统计:")
    print(f"   参与用户: {stats['total_users']}")
    print(f"   总请求数: {stats['total_requests']}")
    
    print("✅ 集成测试完成\n")


async def test_disabled_rate_limiting():
    """测试禁用频率控制的情况"""
    print("🧪 测试禁用频率控制...")
    
    config = TestConfig(
        base_url="http://localhost:8002",
        concurrent_users=3,
        test_duration=10,
        user_session_simulation=False  # 禁用频率控制
    )
    
    tester = HighConcurrencyStressTest(config)
    
    print(f"📊 频率控制状态: {'启用' if config.user_session_simulation else '禁用'}")
    
    # 模拟快速请求
    user_id = 1
    request_times = []
    
    for i in range(3):
        start_time = time.time()
        # 当禁用频率控制时，wait_for_user_turn应该立即返回
        if config.user_session_simulation:
            wait_time = await tester.rate_limiter.wait_for_user_turn(user_id)
        else:
            wait_time = 0  # 禁用时不等待
        
        actual_time = time.time()
        request_times.append(actual_time)
        print(f"   请求{i+1}: 等待{wait_time:.2f}s | 时间: {actual_time:.2f}")
    
    # 分析间隔
    if len(request_times) > 1:
        intervals = []
        for i in range(1, len(request_times)):
            interval = request_times[i] - request_times[i-1]
            intervals.append(interval)
        avg_interval = sum(intervals) / len(intervals)
        print(f"   平均间隔: {avg_interval:.3f}s (应该接近0)")
    
    print("✅ 禁用频率控制测试完成\n")


async def main():
    """主测试函数"""
    print("🚀 用户频率控制功能测试")
    print("=" * 80)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 运行所有测试
    await test_single_user_rate_limiting()
    await test_multi_user_concurrent()
    await test_rate_limiter_stats()
    await test_stress_test_integration()
    await test_disabled_rate_limiting()
    
    print("=" * 80)
    print("✅ 所有测试完成！")
    
    print("\n📋 功能总结:")
    print("   🎯 单用户频率控制: 确保同一用户请求间隔合理")
    print("   🎯 多用户并发支持: 不同用户可以同时访问")
    print("   🎯 动态间隔调整: 根据请求次数调整间隔")
    print("   🎯 统计信息收集: 提供详细的使用统计")
    print("   🎯 可配置参数: 支持自定义间隔范围")
    print("   🎯 可选功能: 支持完全禁用频率控制")
    
    print("\n💡 使用建议:")
    print("   • 正常压测: python tests/stress_test.py --users 100 --duration 600")
    print("   • 调整间隔: python tests/stress_test.py --min-interval 2.0 --max-interval 5.0")
    print("   • 禁用控制: python tests/stress_test.py --disable-rate-limiting")
    print("   • 调试模式: python tests/stress_test.py --debug --users 5 --duration 60")


if __name__ == "__main__":
    asyncio.run(main())
