#!/usr/bin/env python3
"""
测试优化后的压测脚本
验证新增的查询模式和可变文本长度功能
"""

import sys
import os
import asyncio

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tests.stress_test import StressTester, TestConfig


async def test_variable_length_queries():
    """测试可变长度查询生成"""
    print("🧪 测试可变长度查询生成...")
    
    # 创建测试配置
    config = TestConfig(
        base_url="http://localhost:8002",
        concurrent_users=1,
        test_duration=10,
        ramp_up_time=1,
        ramp_down_time=1,
        query_ratio=1.0,
        qa_query_ratio=0.0,
        qa_batch_ratio=0.0,
        qa_create_ratio=0.0,
        health_ratio=0.0,
        connection_pool_size=10,
        request_timeout=30,
        result_buffer_size=1000
    )
    
    # 创建压测器
    tester = StressTester(config)
    
    # 检查可变长度查询
    print(f"✅ 生成的查询类别数: {len(tester.variable_length_queries)}")
    
    for category, queries in tester.variable_length_queries.items():
        print(f"\n📏 {category} 类别:")
        print(f"   - 查询数量: {len(queries)}")
        if queries:
            sample_query = queries[0]
            word_count = len(sample_query.split())
            char_count = len(sample_query)
            print(f"   - 示例长度: {word_count} 词, {char_count} 字符")
            print(f"   - 示例内容: {sample_query[:100]}...")
    
    print(f"\n🎯 查询模式: {tester.query_modes}")
    print(f"🎯 性能模式: {tester.performance_modes}")


async def test_intelligent_query_simulation():
    """测试智能查询模拟"""
    print("\n🧪 测试智能查询模拟...")
    
    config = TestConfig(
        base_url="http://localhost:8002",
        concurrent_users=1,
        test_duration=10,
        ramp_up_time=1,
        ramp_down_time=1,
        query_ratio=1.0,
        qa_query_ratio=0.0,
        qa_batch_ratio=0.0,
        qa_create_ratio=0.0,
        health_ratio=0.0,
        connection_pool_size=10,
        request_timeout=30,
        result_buffer_size=1000
    )
    
    tester = StressTester(config)
    
    # 模拟几次智能查询（不实际发送请求）
    print("📊 模拟智能查询参数组合:")
    
    for i in range(10):
        import random
        query_mode = random.choice(tester.query_modes)
        performance_mode = random.choice(tester.performance_modes)
        length_category = random.choice(list(tester.variable_length_queries.keys()))
        
        print(f"   {i+1}. 模式: {query_mode} | 性能: {performance_mode} | 长度: {length_category}")


def test_configuration_validation():
    """测试配置验证"""
    print("\n🧪 测试配置验证...")
    
    # 测试正常配置
    config1 = TestConfig(
        base_url="http://localhost:8002",
        concurrent_users=100,
        test_duration=600,
        ramp_up_time=30,
        ramp_down_time=30,
        query_ratio=0.70,
        qa_query_ratio=0.15,
        qa_batch_ratio=0.05,
        qa_create_ratio=0.05,
        health_ratio=0.05,
        connection_pool_size=50,
        request_timeout=30,
        result_buffer_size=1000
    )
    
    ratio_sum = (config1.query_ratio + config1.qa_query_ratio + 
                config1.qa_batch_ratio + config1.qa_create_ratio + 
                config1.health_ratio)
    
    print(f"✅ 配置1 - 比例总和: {ratio_sum:.2f}")
    print(f"   - 智能查询: {config1.query_ratio:.1%}")
    print(f"   - 问答查询: {config1.qa_query_ratio:.1%}")
    print(f"   - 批量查询: {config1.qa_batch_ratio:.1%}")
    print(f"   - 创建问答对: {config1.qa_create_ratio:.1%}")
    print(f"   - 健康检查: {config1.health_ratio:.1%}")


async def main():
    """主测试函数"""
    print("🚀 开始测试优化后的压测脚本")
    print("=" * 60)
    
    # 测试可变长度查询生成
    await test_variable_length_queries()
    
    # 测试智能查询模拟
    await test_intelligent_query_simulation()
    
    # 测试配置验证
    test_configuration_validation()
    
    print("\n" + "=" * 60)
    print("✅ 所有测试完成！")
    print("\n📋 优化后的压测脚本特性:")
    print("   • 支持6种查询模式: local, global, hybrid, naive, mix, bypass")
    print("   • 支持5种文本长度: short(50-200), medium(200-800), long(800-2000), very_long(2000-5000), ultra_long(5000-8000)")
    print("   • 专门针对cs_college知识库进行测试")
    print("   • 70%智能查询 + 15%问答查询 + 其他功能测试")
    print("   • 详细的性能统计和分析")
    
    print("\n🎯 使用建议:")
    print("   • 小规模测试: python tests/stress_test.py --users 50 --duration 300")
    print("   • 中等规模测试: python tests/stress_test.py --users 200 --duration 900")
    print("   • 大规模测试: python tests/stress_test.py --users 1000 --duration 1800")
    print("   • 极限测试: python tests/stress_test.py --users 2000 --duration 3600")


if __name__ == "__main__":
    asyncio.run(main())
